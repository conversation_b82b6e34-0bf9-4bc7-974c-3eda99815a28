import os
import subprocess
import sys
import tyro

from pixel3dmm import env_paths


def run_command(cmd, cwd=None):
    """Run a command with proper error handling and path support"""
    print(f"\n[Running] {' '.join(cmd)}\n")
    result = subprocess.run(cmd, cwd=cwd, shell=False)
    if result.returncode != 0:
        print(f"[Error] Command failed with return code {result.returncode}")
        sys.exit(result.returncode)


def main(video_or_images_path : str):
    # Normalize path separators for cross-platform compatibility
    video_or_images_path = os.path.normpath(video_or_images_path)

    if os.path.isdir(video_or_images_path):
        vid_name = os.path.basename(video_or_images_path)
    else:
        vid_name = os.path.splitext(os.path.basename(video_or_images_path))[0]

    # Run cropping with proper subprocess call
    scripts_dir = os.path.join(env_paths.CODE_BASE, 'scripts')
    run_command([
        sys.executable, 'run_cropping.py',
        '--video_or_images_path', video_or_images_path
    ], cwd=scripts_dir)

    # Run MICA with proper subprocess call
    mica_dir = os.path.join(env_paths.CODE_BASE, 'src', 'pixel3dmm', 'preprocessing', 'MICA')
    run_command([
        sys.executable, 'demo.py',
        '-video_name', vid_name
    ], cwd=mica_dir)

    # Run facer segmentation with proper subprocess call
    run_command([
        sys.executable, 'run_facer_segmentation.py',
        '--video_name', vid_name
    ], cwd=scripts_dir)



if __name__ == '__main__':
    tyro.cli(main)