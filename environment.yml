# Note: conda dependencies have only 1 "="
#       pip dependencies have 2 "=="
# Fuse pip dependencies together under one " - pip" item
#       Otherwise, only some of the are installed, because conda creates a temporary requirements.txt file
#       only the last -pip section

name: p3dmm

channels:
  - pytorch
  - conda-forge
  - defaults

dependencies:
  - python=3.9
  - pip
  - jupyter

  # CUDA and PyTorch
  - gcc<12  # Needs to be <12 because nvcc does not like gcc>11
  - gxx
  - torchvision

  -
  - nvidia/label/cuda-11.8.0::cuda-nvcc  # for nvcc
  - nvidia/label/cuda-11.8.0::cuda-cccl
  - nvidia/label/cuda-11.8.0::cuda-cudart
  - nvidia/label/cuda-11.8.0::cuda-cudart-dev  # for cuda_runtime.h
  - nvidia/label/cuda-11.8.0::libcusparse
  - nvidia/label/cuda-11.8.0::libcusparse-dev
  - nvidia/label/cuda-11.8.0::libcublas
  - nvidia/label/cuda-11.8.0::libcublas-dev
  - nvidia/label/cuda-11.8.0::libcurand
  - nvidia/label/cuda-11.8.0::libcurand-dev
  - nvidia/label/cuda-11.8.0::libcusolver
  - nvidia/label/cuda-11.8.0::libcusolver-dev
  - pip:

  - pip:
      - --extra-index-url https://download.pytorch.org/whl/cu118
      - torch==2.7+cu118
      - torchvision==0.22+cu118
      - tyro
      - environs

      - omegaconf
      - dreifus
      - wandb
      - pytorch_lightning
      - opencv-python
      - tensorboard
      - wandb
      - scikit-image
      - pyvista
      - chumpy
      - h5py
      - einops
      - ninja
      - mediapy
      - face-alignment==1.3.3
      - numpy==1.23


      - git+https://github.com/facebookresearch/pytorch3d.git@stable
      - git+https://github.com/NVlabs/nvdiffrast.git

    # for MICA
      - insightface
      - onnxruntime
      - loguru
      - yacs

    # facer
      - distinctipy
      - validators
      - timm
