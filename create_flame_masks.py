#!/usr/bin/env python3
"""
Create a minimal FLAME_masks.pkl file for MICA preprocessing.
This script creates basic face region masks based on the FLAME model structure.
"""

import pickle
import numpy as np
import os

def create_minimal_flame_masks():
    """Create a minimal FLAME masks file with basic face regions."""
    
    # Load the FLAME model to get vertex count
    flame_model_path = "src/pixel3dmm/preprocessing/MICA/data/FLAME2020/generic_model.pkl"
    
    if not os.path.exists(flame_model_path):
        print(f"Error: FLAME model not found at {flame_model_path}")
        return False
    
    with open(flame_model_path, 'rb') as f:
        flame_model = pickle.load(f, encoding='latin1')
    
    num_vertices = flame_model['v_template'].shape[0]
    print(f"FLAME model has {num_vertices} vertices")
    
    # Create basic masks - these are rough approximations
    # In a real FLAME_masks.pkl, these would be carefully defined vertex indices
    
    # Face region (central face area) - roughly 60% of vertices
    face_vertices = np.arange(int(num_vertices * 0.2), int(num_vertices * 0.8))
    
    # Eye regions (small areas around eyes)
    left_eyeball = np.arange(1000, 1100)  # Approximate left eye area
    right_eyeball = np.arange(1100, 1200)  # Approximate right eye area
    left_eye_region = np.arange(950, 1150)
    right_eye_region = np.arange(1050, 1250)
    
    # Other facial features
    nose = np.arange(1500, 1600)
    lips = np.arange(1800, 1900)
    forehead = np.arange(500, 800)
    
    # Ear regions (outer areas)
    left_ear = np.arange(100, 200)
    right_ear = np.arange(200, 300)
    
    # Boundary vertices (edges of the mesh)
    boundary = np.concatenate([np.arange(0, 100), np.arange(num_vertices-100, num_vertices)])
    
    # Create the masks dictionary
    masks = {
        'face': face_vertices,
        'left_eyeball': left_eyeball,
        'right_eyeball': right_eyeball,
        'left_eye_region': left_eye_region,
        'right_eye_region': right_eye_region,
        'eye_region': np.concatenate([left_eye_region, right_eye_region]),
        'nose': nose,
        'lips': lips,
        'forehead': forehead,
        'left_ear': left_ear,
        'right_ear': right_ear,
        'boundary': boundary
    }
    
    # Ensure all indices are within valid range
    for key, mask in masks.items():
        masks[key] = mask[mask < num_vertices]
        print(f"{key}: {len(masks[key])} vertices")
    
    # Create output directory
    output_dir = "src/pixel3dmm/preprocessing/MICA/data/FLAME2020/FLAME_masks"
    os.makedirs(output_dir, exist_ok=True)
    
    # Save the masks
    output_path = os.path.join(output_dir, "FLAME_masks.pkl")
    with open(output_path, 'wb') as f:
        pickle.dump(masks, f)
    
    print(f"Created minimal FLAME masks at: {output_path}")
    return True

if __name__ == "__main__":
    success = create_minimal_flame_masks()
    if success:
        print("FLAME masks created successfully!")
    else:
        print("Failed to create FLAME masks.")
