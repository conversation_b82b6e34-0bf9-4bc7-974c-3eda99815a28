[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "pixel3dmm"  # DON'T FORGET TO REMOVE empty FROM git remote!!!
version = "0.0.1"
description = "<<<ENTER_DESCRIPTION>>>"
authors = [
    { name = "<PERSON>", email = "<EMAIL>" },
]
readme = "README.md"
license = { text = "CC BY-NC 4.0" }
requires-python = ">=3.9.0"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Programming Language :: Python :: 3",
    "Operating System :: OS Independent"
]
# urls = { Documentation = "<<<ENTER_LINK_TO_DOCUMENTATION>>>" }
# Main dependencies
dependencies = [
]

[project.optional-dependencies]
# Development packages, install via <<<PROJECT_NAME>>>[dev]
dev = [
]

[project.scripts]
# E.g., ns-download-data = "scripts.downloads.download_data:entrypoint"

[tool.setuptools.packages.find]
where = ["src"]
include = ["pixel3dmm*"]  # Keep the '*', otherwise submodules are not found
