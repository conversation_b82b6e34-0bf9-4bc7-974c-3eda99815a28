
# This file was generated by 'versioneer.py' (0.19) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2022-06-22T13:57:27-0600",
 "dirty": false,
 "error": null,
 "full-revisionid": "54c52f13713f3d21795926ca4dbb27e16fada171",
 "version": "1.23.0"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
