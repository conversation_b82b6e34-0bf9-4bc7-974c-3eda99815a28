import os
import subprocess
import sys

# Helper for Windows shell
POWERSHELL = sys.platform.startswith('win')
SHELL = True if POWERSHELL else False
PYTHON = sys.executable or 'python'


def run_command(cmd, env=None):
    print(f"\n[Running] {cmd}\n")
    result = subprocess.run(cmd, shell=SHELL, env=env)
    if result.returncode != 0:
        print(f"[Error] Command failed: {cmd}")
        sys.exit(result.returncode)


def get_env_var(name, prompt=None):
    val = os.environ.get(name)
    if not val:
        val = input(prompt or f"Enter value for {name}: ")
    return val


def main():
    print("""
Pixel3DMM Pipeline Runner
------------------------
1. Preprocessing
2. Inference (normals/uv_map)
3. Tracking
4. Visualization
5. Exit
""")
    while True:
        choice = input("Select step (1-5): ").strip()
        if choice == '1':
            video_path = input("Enter path to video or image folder: ").strip().strip('"').strip("'")
            # Replace backslashes with forward slashes for Windows compatibility
            video_path = video_path.replace('\\', '/')
            run_command(f'{PYTHON} scripts/run_preprocessing.py --video_or_images_path "{video_path}"')
        elif choice == '2':
            vid_name = input("Enter video name (without extension): ").strip()
            pred_type = input("Prediction type (normals/uv_map): ").strip()
            run_command(f'{PYTHON} scripts/network_inference.py model.prediction_type={pred_type} video_name={vid_name}')
        elif choice == '3':
            vid_name = input("Enter video name (without extension): ").strip()
            run_command(f'{PYTHON} scripts/track.py video_name={vid_name}')
        elif choice == '4':
            run_command(f'{PYTHON} scripts/viz_head_centric_cameras.py')
        elif choice == '5':
            print("Exiting.")
            break
        else:
            print("Invalid choice. Please select 1-5.")

if __name__ == '__main__':
    main()
