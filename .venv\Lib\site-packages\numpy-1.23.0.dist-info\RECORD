..\..\Scripts\f2py.exe,sha256=k_8BuOQBZKwK_WcLoZTbgoOd6PMsJhoqgDMIdVDZTCY,41418
numpy-1.23.0.dist-info/LICENSE.txt,sha256=vg6QnWgZBt4Cd0OxlFGpvpXHYFvF6fh_Apgl-n7-T80,48809
numpy-1.23.0.dist-info/LICENSES_bundled.txt,sha256=UWGZ0f1YKTYB3hrVHM26mHuBuGTJwRn50NMTMyInvPY,656
numpy-1.23.0.dist-info/METADATA,sha256=apTozUcOiQ3YBkHMwJzvvSZO6kVTH9PeWo1VOPujOgw,2245
numpy-1.23.0.dist-info/RECORD,,
numpy-1.23.0.dist-info/WHEEL,sha256=C6CHup2HLC2Rld8AL5u9w89MYULjdaP5k0k7SG83CcI,102
numpy-1.23.0.dist-info/entry_points.txt,sha256=9Y2FuDxJ4f1QPr79jh1PHP5xheTjHqUjYMwiFml8ZLQ,144
numpy-1.23.0.dist-info/top_level.txt,sha256=4J9lbBMLnAiyxatxh8iRKV5Entd_6-oqbO7pzJjMsPw,6
numpy-1.23.0.dist-info\INSTALLER,sha256=5hhM4Q4mYTT9z6QB6PGpUAW81PGNFrYrdXMj4oM_6ak,2
numpy-1.23.0.dist-info\REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/.libs/libopenblas.FB5AE2TYXYH2IJRDKGDGQ3XBKLKTF43H.gfortran-win_amd64.dll,sha256=lWLIbLq8CRCtmkuHnJkN-FKD3vsbvvr8Y-iqcYMF-IA,35695412
numpy/LICENSE.txt,sha256=vg6QnWgZBt4Cd0OxlFGpvpXHYFvF6fh_Apgl-n7-T80,48809
numpy/__config__.py,sha256=a33ZyX2edrFOVg-u-u8uDbZ4HrkJatlc30LUS9E6Rr8,5198
numpy/__init__.cython-30.pxd,sha256=MZ-QfBGc8pNUong-Glaav9B3kitexUsE-Y0LvWt6fjA,37268
numpy/__init__.pxd,sha256=_K5nNmULH7W9bSf9PN1n-wFVLOa4d5LX891UFiX3dh4,35601
numpy/__init__.py,sha256=IX5QDijh9F2PNhV8HEwZDJZBVRRgEGKIW7OslbEEdAQ,15817
numpy/__init__.pyi,sha256=IlhLbksmyoJEX9OBOugGeHsAy7pzSl2F_CeQHeGubrg,154841
numpy/_distributor_init.py,sha256=JOqSUAVCNcvuXYwzfs41kJFHvhLdrqRfp3ZYk5U71Pc,1247
numpy/_globals.py,sha256=L7ZFuy0AJtvkeecRQ2Rpr2Bzee9Q-aWv1LWMOtYK3aw,4141
numpy/_pyinstaller/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/_pyinstaller/hook-numpy.py,sha256=H5-JbToSn8AvxYFdGvkB8nc4-6RpQLsftBkE-QKNcAc,1462
numpy/_pyinstaller/pyinstaller-smoke.py,sha256=v3H97opxd8Thk3Z0pd5JceX3WJAtE3p4w5bOW-FqOx0,1175
numpy/_pyinstaller/test_pyinstaller.py,sha256=31zWlvlAC2sfhdew97x8aDvcYUaV3Tc_0CwFk8pgKaM,1170
numpy/_pytesttester.py,sha256=qF74TG-8_ox-8-MNnbgAmf8g21B_ZvjxPbXw94oV7Wg,6884
numpy/_pytesttester.pyi,sha256=naZg3QsbkOp4kcjmCJzs3A5vmkLp1WynIgD5hUKHLZI,507
numpy/_typing/__init__.py,sha256=2-TJL4qs1cQHUavShmcg2cFHgZ2SiDZ0EWD51ORUjDg,7253
numpy/_typing/_add_docstring.py,sha256=Fg_yvCZQLR2j-v8AXZ_qP80ad3RQalvVBhDQqaq8g2g,4077
numpy/_typing/_array_like.py,sha256=8BkI-S5iCiS1Mp655zCaxaM7npzZijSwESmrsMpw-tk,3988
numpy/_typing/_callable.pyi,sha256=HgrgeMmWpIJCXT8a8sZVr0AsxjAr14as5bzhtQrtpKk,11079
numpy/_typing/_char_codes.py,sha256=DRzmblPgrH3RPdOr0Hu3M3HA7Zpp_00ZEH_f7Bg5FN4,6027
numpy/_typing/_dtype_like.py,sha256=btTShSCpwEcdTi7r1Kgr-ulblq87eXSsHhcZ9LAR2Y8,5833
numpy/_typing/_extended_precision.py,sha256=mq4J2bIKeD8XGPTauPuAlwqevkywiZZBCCvsiRBvrdA,1154
numpy/_typing/_generic_alias.py,sha256=taR8BHjXLZPdT7pgvjsHCw_hLpxJiSdjbi0uNlNvcAo,7702
numpy/_typing/_nbit.py,sha256=jo5eJF4zrw7QdDw1dIEKLIIPiLM-1tEn95YbDXrdSPs,361
numpy/_typing/_nested_sequence.py,sha256=80evM_MJ_hDCRX-8Yex3TgXB0oJncSHEC_cRsL3FNCI,2747
numpy/_typing/_scalars.py,sha256=V8LKjaaK6eSHVGRKCZIO1MvpGHNjYYMNeHSEfavLgQQ,987
numpy/_typing/_shape.py,sha256=ewHNno8A6Eg3Fl-9JA5jguDk5Q56mEqHjD-vr3VjINY,197
numpy/_typing/_ufunc.pyi,sha256=xB8bEwYJL4JHFL3-ZDW-LRmvNgdJabJa_xP71IRQS9g,11819
numpy/_typing/setup.py,sha256=U68U8y7UUiNDVNYps2dY41DHWdyqeO1Uis0ByzlKPd4,347
numpy/_version.py,sha256=vKBrCcCR9O5H678flae1WtC0n7q76y3H-PGI8KdfrHY,519
numpy/array_api/__init__.py,sha256=5lTDdDPE-ysR-t4mglmJVwrJJA4M41Wsa2CJzeZaGfo,10538
numpy/array_api/_array_object.py,sha256=6HdfOpqujDU_9SzZo2_7rTK5TQ39ysWwPB3f2WuV2uM,44344
numpy/array_api/_constants.py,sha256=l8tz1bWuEXiyzFAHCnx6_yPI4eXoZ4847Zn8yFIO_ow,72
numpy/array_api/_creation_functions.py,sha256=WqPmEGSOQXxoqug_ew0iO1SmEsNjJzmOn4xMRfYT1lA,10401
numpy/array_api/_data_type_functions.py,sha256=Eg3NbGuUnP65kIzPzHfjaC-odCoKXe-MP4UovxTGJ4U,4626
numpy/array_api/_dtypes.py,sha256=uKDX6j3LSsuH0-dHp_dP5IUxEGkUBdIy7qTX5DZGjQg,3850
numpy/array_api/_elementwise_functions.py,sha256=99m6md2SErPzugfltgotPphLEfB3KVgl1bwLUFiGpH4,25501
numpy/array_api/_manipulation_functions.py,sha256=1vL3B4vOGzwXUtzGZxqLRlLsiasAyjD11lJz_soFoIk,3041
numpy/array_api/_searching_functions.py,sha256=eL_gRYZ3Y6Z3OCu6qXl3y79dtnhCsGpeMZYwSVyI9wQ,1504
numpy/array_api/_set_functions.py,sha256=QJoytYiAmBXBD1zYp4QLaEFV-glg0M9hZoxfdmbryK8,2950
numpy/array_api/_sorting_functions.py,sha256=9lzRR1hVFsWe-LYIy7l85JX6HduoIGi7Q8RUZ2avd0s,1803
numpy/array_api/_statistical_functions.py,sha256=lHtSE3pvpuy2TfzYp7F-hnK6nGNJ5bU0_g1FgtMDqk4,3493
numpy/array_api/_typing.py,sha256=MN5OX-mCYT8bLegZFGXoCDRDfHRp5WSSOrS9kmShU9s,1450
numpy/array_api/_utility_functions.py,sha256=LjVxM__wjulID49MF5jeLkCDQuRB50KDUPdvHgAtnmg,861
numpy/array_api/linalg.py,sha256=CaG_Nbc_34ltCgYMmbiIoLIyFi52096B785pEBq6Auk,16947
numpy/array_api/setup.py,sha256=MrEzBh9m4-EV4iekcvUESVM3MW0bHJ5VvXaaTzMFZOU,353
numpy/array_api/tests/__init__.py,sha256=afUKDkt_1ajE1TzYGn4cTp-jMMBfx8riogjk3AePPf0,289
numpy/array_api/tests/test_array_object.py,sha256=B3cuxch1cHIvxLYUMitr5mjcub7k2mqOBdtOV7cYvNY,16145
numpy/array_api/tests/test_creation_functions.py,sha256=0AuzJkFyCLw102ilOCE9lt6q24ERhYVJqqdUKfIhjoQ,5165
numpy/array_api/tests/test_data_type_functions.py,sha256=suUSFbRFNG2oXmAmOadKWUExlQ_cfkZbo4JdYZCIOO0,441
numpy/array_api/tests/test_elementwise_functions.py,sha256=LfMHBMc-nudbY1r4v3a_aFCV6cX8jvDY3kzzyeju-u8,3730
numpy/array_api/tests/test_set_functions.py,sha256=EDUenQPYY47kxAthUP36Vbm0Bi3cgUt2v1c_OiK5Dfg,565
numpy/array_api/tests/test_sorting_functions.py,sha256=_5tZrT-vhYGHDFQCJEj3VWM1CQ0lwbZdW4FVvGz4R_A,625
numpy/array_api/tests/test_validation.py,sha256=UkU6SXeUCkgTuL0GK1NmsK_BVOt6SNeDzUlKbWdr63o,703
numpy/compat/__init__.py,sha256=1hIV4AZtFoLAwunt9IcjI5TSjwz3ONtxopcVj1Mm37g,473
numpy/compat/_inspect.py,sha256=4PWDVD-iE3lZGrBCWdiLMn2oSytssuFszubUkC0oruA,7638
numpy/compat/_pep440.py,sha256=y5Oppq3Kxn2dH3EWBYSENv_j8XjGUXWvNAiNCEJ-euI,14556
numpy/compat/py3k.py,sha256=7pBAYdQJ7YTE0a4yVjnAAM5ROgYUt-ofTWSl_9R-jf8,3744
numpy/compat/setup.py,sha256=PmRas58NGR72H-7OsQj6kElSUeQHjN75qVh5jlQIJmc,345
numpy/compat/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/compat/tests/test_compat.py,sha256=6i0bPM1Nqw0n3wtMphMU7ul7fkQNwcuH2Xxc9vpnQy8,495
numpy/conftest.py,sha256=Q4KMfF1mviYQZkYT0CLDqKdsgPgVgjVu6ljH3yDtmVI,4151
numpy/core/__init__.py,sha256=H4qId_3NJQzdX87t9f33wX7rV8F2KvtF7-mLimL8TQc,5836
numpy/core/__init__.pyi,sha256=nBhfHv0Vy8Cc37vQn3joQShGEvBy4fyo5aBahLSj5Xo,128
numpy/core/_add_newdocs.py,sha256=Qhx9wIZc7puBMmXT4VROUbYDHceZwnd46hIYXqOdXB4,208249
numpy/core/_add_newdocs_scalars.py,sha256=sGojti2m3ZHVZTWhoOiJOZH_pq8i9oDgUG6uD-mwwGk,10543
numpy/core/_asarray.py,sha256=VvzIyBfbu_1BbMeeo1FKSOOZuEfmchcy_WAkDA78dlM,4259
numpy/core/_asarray.pyi,sha256=x8vOeIXbYX7nrKslV8YvljEvnhFHYLCyLnQwNYbKQEU,1093
numpy/core/_dtype.py,sha256=84AysTTmVd7XOlM-miGaTltiG0CxWh-zgm0QBIit02U,10860
numpy/core/_dtype_ctypes.py,sha256=O8tYBqU1QzCG1CXviBe6jrgHYnyIPqpci9GEy9lXO08,3790
numpy/core/_exceptions.py,sha256=fXtzao8C8_C81LF6HPsBsgMER0oCrQrncseFN-gcljo,8615
numpy/core/_internal.py,sha256=s5qC6X--ApY1UYnA-H0ZG2SYdOAWIxR7Be-MyRbkn-A,29153
numpy/core/_internal.pyi,sha256=HBjDwzplwD0lAEzYt4Xl4y2O_RjBLP1jcuT9XudVPLU,1062
numpy/core/_machar.py,sha256=DbHjMfwBo9B3XHuGzJ7npdtvPmFAXoPZNvNMoBobDQI,11841
numpy/core/_methods.py,sha256=OMFW7TAMKgmQsjUzMlfJVaa6XzwZiiBZBfVqDkzb54g,11180
numpy/core/_multiarray_tests.cp310-win_amd64.pyd,sha256=7jlUa8UpvCMYyiFy-PX9JKxM1TabKjy1lozsn9ISPxk,65536
numpy/core/_multiarray_umath.cp310-win_amd64.pyd,sha256=oHdA0AaJXAtWdxiJ8ti0OGQMDYb2xOUFYoVBIYnGV-M,2681856
numpy/core/_operand_flag_tests.cp310-win_amd64.pyd,sha256=bo64P2NNr6HY4PlEKu_n8_ENCtbNDWaBgHl90HnVEe8,12288
numpy/core/_rational_tests.cp310-win_amd64.pyd,sha256=r6v-4hPfXvb_PcpjgHZCHKf3Rmq-GuUb1mS3EasuLbE,40448
numpy/core/_simd.cp310-win_amd64.pyd,sha256=aS_2f7wTSNKdxDo2M2w-J0rusaEnI5DCBVgo0LIjvG0,1201664
numpy/core/_string_helpers.py,sha256=xFVFp6go9I8O7PKRR2zwkOk2VJxlnXTFSYt4s7MwXGM,2955
numpy/core/_struct_ufunc_tests.cp310-win_amd64.pyd,sha256=jbfeJGd16v-feujM48ivwBxJTtaNaXub6jb2YkW_NTg,13824
numpy/core/_type_aliases.py,sha256=F488sw89mnAmAfXDWN0oHeuqhC9YPRpFlznoo02CllY,7734
numpy/core/_type_aliases.pyi,sha256=uccb0D594Wq_yu0ez4ARoW5XLj6R1vy-E-vWJXywnhk,387
numpy/core/_ufunc_config.py,sha256=QkZB3Ddvj0AsOM2enPW2B9nq0q7ntSavgeQzXPEehjY,13828
numpy/core/_ufunc_config.pyi,sha256=JZC0oQ--yOB5ABhgXeDGM-u3U4x0mA8BCk8vJTMDHPM,1080
numpy/core/_umath_tests.cp310-win_amd64.pyd,sha256=-YEfjJTomE4lOjyYExWj1cRkFDMJmIgC0gk1_iM6WLk,29696
numpy/core/arrayprint.py,sha256=Pt6pbluj4l8XK5LxW0H1v5Z1XzHAAWE49eiQp3ivj1M,64544
numpy/core/arrayprint.pyi,sha256=8fKy1vuqDh5XbDUg9824pp1QKKf2fIIedlzwtU2DCko,4570
numpy/core/cversions.py,sha256=FISv1d4R917Bi5xJjKKy8Lo6AlFkV00WvSoB7l3acA4,360
numpy/core/defchararray.py,sha256=oBv3bktpIDEygbutJ3BqdBO-rakxpSICg0GQCm18B2E,72572
numpy/core/defchararray.pyi,sha256=e9Xi4m3OgIml9TftRkpSM6uctdLaNdEbqnZtkF8kSJ0,9637
numpy/core/einsumfunc.py,sha256=24VA1C9Urm3HOCDEJgYgNn3miEmd3F_1Gb99zXDJykA,53312
numpy/core/einsumfunc.pyi,sha256=JQDp4kCvrdn1-ZOhjrVF6hBv3tYOwAIHj2WK-Ar6hw4,3751
numpy/core/fromnumeric.py,sha256=3qjMvUHn_k-oKRXJnZL1UN1GxC6Dsx9KUBTguwfzbL4,127264
numpy/core/fromnumeric.pyi,sha256=ThsXpqMeh8DWUGZQi5TiDBZXUxhXp9auC3Vb_tLVTO0,24521
numpy/core/function_base.py,sha256=zfEh8nDUBBu37nl-sNUA3VuaBLvr9z7tgE6ImJqNhmw,19548
numpy/core/function_base.pyi,sha256=iqCNWDs25nUsCXqt2NBwxNqewlEJYfFiIraqBJXv1h4,4912
numpy/core/generate_numpy_api.py,sha256=RQCY7uoIZRyaCVar0lowJDq5MxshlqsrXcZgDngMOUA,7243
numpy/core/getlimits.py,sha256=gwHErBCLkJtl7SaFWrlPdD3ZoMWgyDherRR_Rd6Rdz8,24764
numpy/core/getlimits.pyi,sha256=rRrU4RZYBrsczGZq6_VRCBNwUKyrqPAHARXXLQuw950,88
numpy/core/include/numpy/.doxyfile,sha256=kISUwZgVeWl649njq6TmhQiyqzPciaZ_QS9e1Q62pOE,60
numpy/core/include/numpy/__multiarray_api.h,sha256=gox5Va_iStLemxn3YmjKpAFf3U5bAgsPDEZMo2aPJ1U,63867
numpy/core/include/numpy/__ufunc_api.h,sha256=H7-TsFbz-CmyBFceTYhNcOm-GltjuAVLebBjctaRtA4,12925
numpy/core/include/numpy/_neighborhood_iterator_imp.h,sha256=sItfdxPWMM6KEBwCpNLC3VoSQVb4usMUC40zOK4zE_s,1967
numpy/core/include/numpy/_numpyconfig.h,sha256=hcnzpXmNhBtYvFVrCpMxt0nDZjoM2Ff4kmJ7nDFdT40,851
numpy/core/include/numpy/arrayobject.h,sha256=f1YdhtzB7wAHDQwmClaFwOl3U-cxkm2UJqzpfQNyhOs,294
numpy/core/include/numpy/arrayscalars.h,sha256=ONfMOpGH6KLorCBcc8265NkFse4x1sZ5ZlnMnC4kM60,4000
numpy/core/include/numpy/experimental_dtype_api.h,sha256=HE1zXtn5zaJW8jjNJewNRFM3s7t9I1T_TDXVtAsYZY0,20416
numpy/core/include/numpy/halffloat.h,sha256=qYgX5iQfNzXICsnd0MCRq5ELhhfFjlRGm1xXGimQm44,2029
numpy/core/include/numpy/libdivide/LICENSE.txt,sha256=1UR2FVi1EIZsIffootVxb8p24LmBF-O2uGMU23JE0VA,1039
numpy/core/include/numpy/libdivide/libdivide.h,sha256=F9PLx6TcOk-sd0dObe0nWLyz4HhbHv2K7voR_kolpGU,82217
numpy/core/include/numpy/multiarray_api.txt,sha256=QaoBXpkigoWJOMy6y2JfqnizZtN0dmoApQSkSBLE8B0,59502
numpy/core/include/numpy/ndarrayobject.h,sha256=Ar9_P9KCJBRECa8ZvJhlODs4Z_S_vI9HpQKjVbauzUs,10442
numpy/core/include/numpy/ndarraytypes.h,sha256=T86wHgbLBOR8m9i3oM6ToREXXPP8ujDWd8pGhnit8l4,71042
numpy/core/include/numpy/noprefix.h,sha256=QYUdrHLYEaooNNXnazQYo5WpqPxHedEVXlOAGP7oECo,7041
numpy/core/include/numpy/npy_1_7_deprecated_api.h,sha256=dX_2cI125SpW88dJEtryt-wO4pHxzKR4CcODfjFwtms,4451
numpy/core/include/numpy/npy_3kcompat.h,sha256=GvHO40vGFTrpAsjyoV1oqDj_ReGSum7jnDTAk6D3Zfg,16587
numpy/core/include/numpy/npy_common.h,sha256=t49cyx1laCI6wz_T8FJKf8DCad5RuQW53xPHXAqp18U,40137
numpy/core/include/numpy/npy_cpu.h,sha256=tlYFBhOC6yswF9fZTxWlH2U8nBoOchnygVU5wiTR-5c,4732
numpy/core/include/numpy/npy_endian.h,sha256=G3x4fuvRgY6_Y0AWiJaQ5ZbtmMRRt_QUnYCwkqrHhPE,2863
numpy/core/include/numpy/npy_interrupt.h,sha256=9KhrOhicl4TW3pP6wugWNz5pDDvp9SroisKE04Bbaew,2004
numpy/core/include/numpy/npy_math.h,sha256=4dmF8S55O9yCYroaRGpnpsiqc7Jtmfc0Vlc6JldVfvc,21969
numpy/core/include/numpy/npy_no_deprecated_api.h,sha256=jIcjEP2AbovDTfgE-qtvdP51_dVGjVnEGBX86rlGSKE,698
numpy/core/include/numpy/npy_os.h,sha256=OsRZwuIzGcPnqKQWMg2OuxXDxPZnvKB0JWnbTg9DiXA,1104
numpy/core/include/numpy/numpyconfig.h,sha256=ObgLFZCGjgDpXqIEWZD8h_xJjM2AcHFUJL-uxW9z-cA,2352
numpy/core/include/numpy/old_defines.h,sha256=jGkDx_FahMvHMTuoWFvx5g5bCV8btUj9pgYNoo_PtwA,6592
numpy/core/include/numpy/oldnumeric.h,sha256=2d5tfBGHavnHpQJUvX90ZJFkgsEF8TlpqXhHNUuE_54,931
numpy/core/include/numpy/random/bitgen.h,sha256=_H0uXqmnub4PxnJWdMWaNqfpyFDu2KB0skf2wc5vjUc,508
numpy/core/include/numpy/random/distributions.h,sha256=mzuU4dT5sv78rg5IHy6Ip1rHGfrbstMtk_EIja5_Jds,10074
numpy/core/include/numpy/ufunc_api.txt,sha256=Qnbm8bUW9JXlOmCaX-cGztnUK-QKj0ecPPeNm6ZEgv0,7533
numpy/core/include/numpy/ufuncobject.h,sha256=TW8ccfYFcCMWZDHGyLkva_wlk9HZ2ewdphgHmDRAeKo,12252
numpy/core/include/numpy/utils.h,sha256=vzJAbatJYfxHmX2yL_xBirmB4mEGLOhJ92JlV9s8yPs,1222
numpy/core/lib/npy-pkg-config/mlib.ini,sha256=mQBSOI6opCVmMZK4vwIhLe5J7YevO3PbaHsI0MlJGHs,151
numpy/core/lib/npy-pkg-config/npymath.ini,sha256=5dwvhvbX3a_9toniEDvGPDGChbXIfFiLa36H4YOR-vw,380
numpy/core/lib/npymath.lib,sha256=n3ir6dk4NGPKQu1ZaMVQbZsyXa01veglzEqrlNFut84,150338
numpy/core/memmap.py,sha256=_XXONlK35hVQqljype2sNt43fNAhBtBlkJwnedqTrnw,12025
numpy/core/memmap.pyi,sha256=pwOLGTEk20Z2Tao7hqxLDqfCHrO10otgozEsUO-bPeo,58
numpy/core/multiarray.py,sha256=if5DppedQQUR5gr0QqX5B2DpAk0v-UuuE-LUNG07Upc,57124
numpy/core/multiarray.pyi,sha256=OGOqHGMMLGdRPC5YoM6L4wkMDgeXMc3OOF_PRFHXTHQ,25406
numpy/core/numeric.py,sha256=YIa8hrGW4Geo-IylpjB7HzKKE10YuMyixjQzlS5hWJQ,79998
numpy/core/numeric.pyi,sha256=MsJ7A4oi3b8-nVJIN6FcVDz94SYNvd46t0b5lM7-lR0,14107
numpy/core/numerictypes.py,sha256=2Z0X2GNBqJ1oy96xI_d6-r9Gn3-kRZhl2DE3na43y0o,17941
numpy/core/numerictypes.pyi,sha256=MzJCuO3afaM89bG3FbLHSfFbZXkkozfl6s_m08twJWQ,3549
numpy/core/overrides.py,sha256=d5CdQvc1y_uY4vJpeKPXueOao4-22zuU1LzQTzp5Abw,7501
numpy/core/records.py,sha256=qCYrXWgdiyAwbiaos3mgUiLZBQyvzlu3RAlypLkpDa0,38644
numpy/core/records.pyi,sha256=6qSYt2FKZytTswMFqcTrrqVi4HEbLXh0_gB2Graq78o,5926
numpy/core/setup.py,sha256=tIKTMST4y9NrT8afEeqSW8x4S2JDaiFVVz8zuCIlRqM,53829
numpy/core/setup_common.py,sha256=CmkItttNAAP7is3lqRvBQQceYdeIpKRiRKvcsfd0oiQ,20747
numpy/core/shape_base.py,sha256=LBlRtpgdQi18oDG6EvVZV1bARN0VcY_inT7nP_7RHwM,29883
numpy/core/shape_base.pyi,sha256=IwBAA3xWW0Lsyin0VU3HOs4T9ASb-6DEBSegDJ9wbWo,1809
numpy/core/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/core/tests/_locales.py,sha256=PAV24baH5MIl_gH_VBi5glF-7kgifkK00WnAP0Hhc60,2266
numpy/core/tests/data/astype_copy.pkl,sha256=lWSzCcvzRB_wpuRGj92spGIw-rNPFcd9hwJaRVvfWdk,716
numpy/core/tests/data/generate_umath_validation_data.cpp,sha256=72bn3HBT3sS7uIIWKKSgW1VtNZpRlSZM-OsaKbh0cQA,6010
numpy/core/tests/data/recarray_from_file.fits,sha256=NA0kliz31FlLnYxv3ppzeruONqNYkuEvts5wzXEeIc4,8640
numpy/core/tests/data/umath-validation-set-README.txt,sha256=GfrkmU_wTjpLkOftWDuGayEDdV3RPpN2GRVQX61VgWI,982
numpy/core/tests/data/umath-validation-set-arccos.csv,sha256=8mDga_qwTZoPMm1UHPAqjLfBKTHTW5PT5sTeSQhg8pI,62794
numpy/core/tests/data/umath-validation-set-arccosh.csv,sha256=RN30zA_HBqlPb4UwCfk3gQMYNSopV765CQWnhG2Lx0g,62794
numpy/core/tests/data/umath-validation-set-arcsin.csv,sha256=TMvZI0veNaOHupfGPvS_pTRfX0yH33SoaQWT6Q9Epsc,62768
numpy/core/tests/data/umath-validation-set-arcsinh.csv,sha256=GFRD_4CZTEH47C71CWC6pVSWkJFMgxdii3rJXV3RAkw,61718
numpy/core/tests/data/umath-validation-set-arctan.csv,sha256=EFyJjE5dr5VBPLKlFf_7ZVI_s8Wx7FswdHEzs1mpYr8,61734
numpy/core/tests/data/umath-validation-set-arctanh.csv,sha256=0_cOGarj-biMitr6L1ZsBafWfDpecSOf-pk96wVOpIA,62768
numpy/core/tests/data/umath-validation-set-cbrt.csv,sha256=FFi_XxEnGrfJd7OxtjVFT6WFC2tUqKhVV8fmQfb0z8o,62275
numpy/core/tests/data/umath-validation-set-cos.csv,sha256=qeR-Dg9vJB2Xz63q2b50tWUO1i0TWHMgYC75XGiy-AY,60497
numpy/core/tests/data/umath-validation-set-cosh.csv,sha256=FoMRNGCkmjaAspkoZ6EOTRkcUCUxnWdcq2NHfMyaPXg,62298
numpy/core/tests/data/umath-validation-set-exp.csv,sha256=q7AFuKS3D9HRm01wby5b0aZhbBF-eFmniO-NIyuHEpo,17903
numpy/core/tests/data/umath-validation-set-exp2.csv,sha256=ruPfs9R5l8NU43eP3HSiJYMzJMQkD0W5XwpkFRcVZNI,60053
numpy/core/tests/data/umath-validation-set-expm1.csv,sha256=onF_9BcC7wV_dkSRgazWVe9WaEOijyoBGUYSmaEc7OM,61728
numpy/core/tests/data/umath-validation-set-log.csv,sha256=vp1nbu--u7rV8dg9bDLUteLOfZBe5s4Uwyhll15g4AY,11963
numpy/core/tests/data/umath-validation-set-log10.csv,sha256=Gy6aRCYcWMBxTLIOLY9_zWymevNOGlc8cy5fjo1NnCg,70551
numpy/core/tests/data/umath-validation-set-log1p.csv,sha256=5hnT1xXhP9lCmLx_qZ3FMFrujTKdJS-5SZCsKX3yke0,61732
numpy/core/tests/data/umath-validation-set-log2.csv,sha256=ihQyfW16BQYldFbg2v7HErkm1efgGuops7tv7pwVCPI,70546
numpy/core/tests/data/umath-validation-set-sin.csv,sha256=aGuZ1Hr8i6PViQGA-UbAUjcXqAdcuAn9AqtFShv09Vg,59981
numpy/core/tests/data/umath-validation-set-sinh.csv,sha256=RI_UkXTgvcO2VAbFFZZSmR--RQbxSdQePc8gnfkLICE,61722
numpy/core/tests/data/umath-validation-set-tan.csv,sha256=H2Z3jO3nV6u0rXYPes3MnI4OSdaKcStfjjKoMiKsyFQ,61728
numpy/core/tests/data/umath-validation-set-tanh.csv,sha256=xSY5fgfeBXN6fal4XDed-VUcgFIy9qKOosa7vQ5v1-U,61728
numpy/core/tests/examples/cython/checks.pyx,sha256=IZwVIJAOuV8aAYZB-zMq6kif5Hqr8FkjY7eY6220Mg8,647
numpy/core/tests/examples/cython/setup.py,sha256=JvbvFHHdkZx_eajlwMTtqHijLD-TZwXCZlRySAe-y94,521
numpy/core/tests/examples/limited_api/limited_api.c,sha256=znFZtXQ0y9tOcovvZSjq9GrxBfx3st_s67Fru4owwUg,361
numpy/core/tests/examples/limited_api/setup.py,sha256=N7kqsVp4iIE20IebigEJUW3nW2F0l6Vthb5qNvKHBmM,457
numpy/core/tests/test__exceptions.py,sha256=PmFZh81gSxCUKkNs2wDaMICLM5uGp6lI92OynAd-KtM,2934
numpy/core/tests/test_abc.py,sha256=KbyIH9nsGOnXQ8VtnwU6QyUgaFDC0XfEs476CnJ_-Wg,2382
numpy/core/tests/test_api.py,sha256=hZIDt1ZDY0duKNdhY3D0n8fV95Yr9YRP9V_-469PF6c,23080
numpy/core/tests/test_argparse.py,sha256=W4ys2ZU9B-LQwcuY16KNb3QExGVhxV32yJ1pG5-zF7w,2039
numpy/core/tests/test_array_coercion.py,sha256=YU3ulIFMFZUKbk9LsZ-VJuOOG5DN7DC7k3zztRbVoSA,29842
numpy/core/tests/test_array_interface.py,sha256=KYc12q1KwxJOpEsRfqYDVccaWoc3FdcYn5ULh9rG24c,7812
numpy/core/tests/test_arraymethod.py,sha256=JxKpNRGGQvKzfQIy_xMB47t7J5gKUjWlHicEyfdzMOQ,3661
numpy/core/tests/test_arrayprint.py,sha256=FOo5rBHRKGOtQB29fA-zqQERK8cAAn3GzIyaDVTRVY4,38119
numpy/core/tests/test_casting_unittests.py,sha256=3e8XTm-9L1aIwjJS4ONmr-_LMNrZsWCdmPMyB_GiCf8,34677
numpy/core/tests/test_conversion_utils.py,sha256=4PJfmBFJ0N-K0Xqf1SBRxryyP0bfu5UF_p4el1YcPI4,6767
numpy/core/tests/test_cpu_dispatcher.py,sha256=vWLZWTYbMGanntWj1EfPcJMce0J4fizoPjmPVNGzIrs,1563
numpy/core/tests/test_cpu_features.py,sha256=35qPkNMzwA-oMYuZ3EikWUppT4KazKln0SSEE28W9RY,7354
numpy/core/tests/test_custom_dtypes.py,sha256=H0czaWzy9xtUnz7pY3Fr4w-q-xPIIwShoOcFD-qou-c,7878
numpy/core/tests/test_cython.py,sha256=B8vRQPEcU9i99nQGocKfDAPRceDyYoHBQstp38s6bnE,3670
numpy/core/tests/test_datetime.py,sha256=ipWmvL1UQGRtMA0mIgvqYQFEuXDppoFyxiygRRZSj2w,117485
numpy/core/tests/test_defchararray.py,sha256=VhAlRKEJSrcWcrZbZruagw1170409taptPGauwxp-HM,25256
numpy/core/tests/test_deprecations.py,sha256=pW5uRde4WjY8_QVfs5vS-8Rp7qaWATtN6Yx0PrMtLis,48802
numpy/core/tests/test_dlpack.py,sha256=-0Wd49Q1fuCa4UOWMLhvwUg2zuBwBfDHrhMbUEOKFcg,3625
numpy/core/tests/test_dtype.py,sha256=FVw06atKJ4HCLOQcHf2X3O1y-1oDjzGtL-_C6wMS6RA,71576
numpy/core/tests/test_einsum.py,sha256=soc6dyiVfP8jm1Wm2LZU6pN1zqSwXkZ-XcdAXY8xuEQ,51247
numpy/core/tests/test_errstate.py,sha256=kNCMfKs1xgXUrIRQM4qB8fEkVbREVE-p-yG2hsuHjd4,2125
numpy/core/tests/test_extint128.py,sha256=b8vP_hDPltdHoxWBn2vAmOgjJe2NVW_vjnatdCOtxu8,5862
numpy/core/tests/test_function_base.py,sha256=FjbJ8JMRcbBxllRXRZhxbE6nlfP1HL8I4kfyW8RKr0s,14820
numpy/core/tests/test_getlimits.py,sha256=zPOV3p6sUfrJMdMNd6XC3cQHtDhR38gG4Tzee0DSLVI,5352
numpy/core/tests/test_half.py,sha256=7vZngTRJJ45h4XUFws9fpHI_gYKPq9pz_DKUhZf8lEk,24370
numpy/core/tests/test_hashtable.py,sha256=6RRLuIzT9HAAJIOpLQijbv0PTdOEyWzZJ0mTH8lhJqs,1041
numpy/core/tests/test_indexerrors.py,sha256=iJu4EorQks1MmiwTV-fda-vd4HfCEwv9_Ba_rVea7xw,5263
numpy/core/tests/test_indexing.py,sha256=p8qEW7EYaOW8KuswWUUOHBS0HgJcQm7tgEztV9P41-I,55644
numpy/core/tests/test_item_selection.py,sha256=8AukBkttHRVkRBujEDDSW0AN1i6FvRDeJO5Cot9teFc,3665
numpy/core/tests/test_limited_api.py,sha256=KjnJNH9UQcxfxtBfHk5oehYQd2oot28rxtmVFFLXONc,1116
numpy/core/tests/test_longdouble.py,sha256=rUSdB2t2pyepIVWyaCL9YaCK_e8G64VI7HumbNKmzWg,13412
numpy/core/tests/test_machar.py,sha256=pauEGVLnT_k8tD0Meu_s_uxutPvSwpOPywlFoizehMg,1097
numpy/core/tests/test_mem_overlap.py,sha256=69RZVSFbEN3hGXhJB9PeRHiRMWLAPru2C-3xC2AAckc,30015
numpy/core/tests/test_mem_policy.py,sha256=b-h_ZwR98vt1G6c59toF1lHFIZc0fIzQLDdsPYWBYSo,16292
numpy/core/tests/test_memmap.py,sha256=2h9gFYyNGaGjXkcmQ1uL5isIVgs-tcafkcsI2eoKnKY,7684
numpy/core/tests/test_multiarray.py,sha256=Hj8gUs3I7VZyQ1d381llPf1dhll7LeDD2hD61LxKNbY,374227
numpy/core/tests/test_nditer.py,sha256=b6mFyn_YIu-IS9Iu_XZMcYglqNgTPDEXEMGuGsRGDic,131241
numpy/core/tests/test_numeric.py,sha256=O0oA1GK7sRVgd5odWFE7E5FIoksfcx3bacrN35Rxvp8,140294
numpy/core/tests/test_numerictypes.py,sha256=CCuE6wprrdHOYhTmQoI3mViuJTGfJDbPO6fmW3XpxbY,21706
numpy/core/tests/test_overrides.py,sha256=9jPVYSMVzoqO6bVFmV5-0n7ad3HzIInmLrJnbh86cPw,20778
numpy/core/tests/test_print.py,sha256=I3-R4iNbFjmVdl5xHPb9ezYb4zDfdXNfzVZt3Lz3bqU,6937
numpy/core/tests/test_protocols.py,sha256=Etu0M6T-xFJjAyXy2EcCH48Tbe5VRnZCySjMy0RhbPY,1212
numpy/core/tests/test_records.py,sha256=910AOqDvIhCDsrmCQk737UhxDnQU3GSB5a2-w0h9hWM,20782
numpy/core/tests/test_regression.py,sha256=F28SnorvvlplWF1zmXg8E9GKB41L3PCWCE_u5ayNl_Q,93663
numpy/core/tests/test_scalar_ctors.py,sha256=ilVfUULT72ALjt1WH0B54winaYbWrRy62AewkvugJao,3803
numpy/core/tests/test_scalar_methods.py,sha256=aLOSld2H2HSTqjIRtsbhLeD-RlPibw7dSJHNC-2hlRs,7698
numpy/core/tests/test_scalarbuffer.py,sha256=pt5NLIVo7EPY9Ih5FJZuy2saFEmyc5YPqg6eCVT2gq8,5741
numpy/core/tests/test_scalarinherit.py,sha256=OqjtqccS5mphfhoQxjYwf5lK6sIVhMRYV4y2V1kuq-s,2479
numpy/core/tests/test_scalarmath.py,sha256=dt4oo2Gyf9oSpzmriT3I2ZjbSVI_pVW2BqtfEQK8uWk,41818
numpy/core/tests/test_scalarprint.py,sha256=5E5njBLwKYa3f3qnGu4XwaFToBUN4oUZ8F0nnf109PY,19076
numpy/core/tests/test_shape_base.py,sha256=tndYVsnh6zATYP7q5PNgy_inq7OPWCP5z7ua6uZf3oQ,28010
numpy/core/tests/test_simd.py,sha256=LPHB598fiA3A6iIpIpO0dTWI4Ghh6Ds6syD04qcBbxE,38487
numpy/core/tests/test_simd_module.py,sha256=Bpbg_3YKO4Nu4Jm8p_QIlhrz_3tLIO6ketPhJmA_hZU,3855
numpy/core/tests/test_ufunc.py,sha256=MvagqbayFjHpxj_VvQScJ-TrildnKbWQVuKmPaJCKIY,110742
numpy/core/tests/test_umath.py,sha256=dOP96ioDXXxet_J6RLHGAQa24mPtqRVmkHmUts2mDBI,157799
numpy/core/tests/test_umath_accuracy.py,sha256=QCsqxxu2x97FT-hEa6nVxBnunODuudTprGCpJqUgV_s,3290
numpy/core/tests/test_umath_complex.py,sha256=TColPReMW9GpkZxDwU3NacXWO3fSAEWzpPqCGc1056U,23837
numpy/core/tests/test_unicode.py,sha256=c6SB-PZaiNH7HvEZ2xIrfAMPmvuJmcTo79aBBkdCFvY,12915
numpy/core/umath.py,sha256=IE9whDRUf3FOx3hdo6bGB0X_4OOJn_Wk6ajnbrc542A,2076
numpy/core/umath_tests.py,sha256=IuFDModusxI6j5Qk-VWYHRZDIE806dzvju0qYlvwmfY,402
numpy/ctypeslib.py,sha256=nvfW3iT0-DOVAsd7wbFuMd_aHVXaYAPudIouL7OYL1I,18007
numpy/ctypeslib.pyi,sha256=Xf1wbREr577XCOsZBICgycP3AjGGBV98cU0UAisFpYo,8213
numpy/distutils/__config__.py,sha256=a33ZyX2edrFOVg-u-u8uDbZ4HrkJatlc30LUS9E6Rr8,5198
numpy/distutils/__init__.py,sha256=sh1TV9_aW0YWvmHfBPtbZKCRcZTN6BnxKV-mIAG2vuY,2138
numpy/distutils/__init__.pyi,sha256=6KiQIH85pUXaIlow3KW06e1_ZJBocVY6lIGghNaW33A,123
numpy/distutils/_shell_utils.py,sha256=9pI0lXlRJxB22TPVBNUhWe7EnE-V6xIhMNQSR8LOw40,2704
numpy/distutils/armccompiler.py,sha256=L9NTe61a7Qonkfcv3SLg6DhjuSbDou7PARZL0RF6jsU,1071
numpy/distutils/ccompiler.py,sha256=pLDO0y4AKFihTm1jEB5f4H4BC6O8XKEKwUOnRRfeDd0,28556
numpy/distutils/ccompiler_opt.py,sha256=mdIS4WdHxnc5ILXPzHgPbnN0WBGy4oKBQRcApa8SPt4,102406
numpy/distutils/checks/cpu_asimd.c,sha256=Nit4NvYvo3XWtBKeV6rmIszdNLu9AY81sqMFCTkKXBE,845
numpy/distutils/checks/cpu_asimddp.c,sha256=bQP32IzQZANu9aFu3qkovLYJXKCm0bJ6srsO5Ho2GKI,448
numpy/distutils/checks/cpu_asimdfhm.c,sha256=xJjmEakgtmK9zlx2fIT6UZ4eZreLzdCoOVkkGPyzXFA,548
numpy/distutils/checks/cpu_asimdhp.c,sha256=0eTZ2E1Gyk3G5XfkpSN32yI9AC3SUwwFetyAOtEp5u4,394
numpy/distutils/checks/cpu_avx.c,sha256=69aCE28EArV-BmdFKhCA5djgNZAZtQg2zdea3VQD-co,799
numpy/distutils/checks/cpu_avx2.c,sha256=207hFoh4ojzMAPQ53ug_Y5qCFIgZ1e8SdI1-o2jzdB4,769
numpy/distutils/checks/cpu_avx512_clx.c,sha256=CfPjudkRZ9_xygLVOySSEjoAfkjjfu4ipkWK4uCahbU,864
numpy/distutils/checks/cpu_avx512_cnl.c,sha256=eKCPRk6p1B0bPAyOY0oWRKZMfa-c5g-skvJGGlG5I4Y,972
numpy/distutils/checks/cpu_avx512_icl.c,sha256=Zt8XOXZL85Ds5HvZlAwUVilT6mGbPU44Iir44ul6y2Y,1030
numpy/distutils/checks/cpu_avx512_knl.c,sha256=ikHHx87EfJ3-sjJoT5QeSIvmrHrO4oC9KJzCB-wp5BE,981
numpy/distutils/checks/cpu_avx512_knm.c,sha256=iVdJnZ5HY59XhUv4GzwqYRwz2E_jWJnk1uSz97MvxY0,1162
numpy/distutils/checks/cpu_avx512_skx.c,sha256=aOHpYdGPEx2FcnC7TKe9Nr7wQ0QWW20Uq3xRVSb4U90,1036
numpy/distutils/checks/cpu_avx512cd.c,sha256=zIl7AJXfxqnquZyHQvUAGr9M-vt62TIlylhdlrg-qkE,779
numpy/distutils/checks/cpu_avx512f.c,sha256=ibW0zon6XGYkdfnYETuPfREmE5OtO0HfuLTqXMsoqNA,775
numpy/distutils/checks/cpu_f16c.c,sha256=QxxI3vimUAkJ4eJ83va2mZzTJOk3yROI05fVY07H5To,890
numpy/distutils/checks/cpu_fma3.c,sha256=Cq0F_UpVJ4SYHcxXfaYoqHSYvWRJzZsB8IkOVl8K2ro,839
numpy/distutils/checks/cpu_fma4.c,sha256=Xy0YfVpQDCiFOOrCWH-RMkv7ms5ZAbSauwm2xEOT94o,314
numpy/distutils/checks/cpu_neon.c,sha256=I-R8DHE6JfzqmPpaF4NTdWxq5hEW-lJZPjSjW8ynFgo,619
numpy/distutils/checks/cpu_neon_fp16.c,sha256=6hdykX7cRL3ruejgK3bf_IXGQWol8OUITPEjvbz_1Hc,262
numpy/distutils/checks/cpu_neon_vfpv4.c,sha256=IY4cT03GTrzEZKLd7UInKtYC0DlgugFGGrkSTfwwvmU,630
numpy/distutils/checks/cpu_popcnt.c,sha256=Jkslm5DiuxbI-fBcCIgJjxjidm-Ps_yfAb_jJIZonE8,1081
numpy/distutils/checks/cpu_sse.c,sha256=XitLZu_qxXDINNpbfcUAL7iduT1I63HjNgtyE72SCEo,706
numpy/distutils/checks/cpu_sse2.c,sha256=OJpQzshqCS6Cp9X1I1yqh2ZPa0b2AoSmJn6HdApOzYk,717
numpy/distutils/checks/cpu_sse3.c,sha256=AmZkvTpXcoCAfVckXgvwloutI5CTHkwHJD86pYsntgk,709
numpy/distutils/checks/cpu_sse41.c,sha256=5GvpgxPcDL39iydUjKyS6WczOiXTs14KeXvlWVOr6LQ,695
numpy/distutils/checks/cpu_sse42.c,sha256=8eYzhquuXjRRGp3isTX0cNUV3pXATEPc-J-CDYTgTaU,712
numpy/distutils/checks/cpu_ssse3.c,sha256=QXWKRz5fGQv5bn282bJL4h_92-yqHFG_Gp5uLKvcA34,725
numpy/distutils/checks/cpu_vsx.c,sha256=gxWpdnkMeoaBCzlU_j56brB38KFo4ItFsjyiyo3YrKk,499
numpy/distutils/checks/cpu_vsx2.c,sha256=ycKoKXszrZkECYmonzKd7TgflpZyVc1Xq-gtJqyPKxs,276
numpy/distutils/checks/cpu_vsx3.c,sha256=pNA4w2odwo-mUfSnKnXl5SVY1z2nOxPZZcNC-L2YX1w,263
numpy/distutils/checks/cpu_vsx4.c,sha256=SROYYjVVc8gPlM4ERO--9Dk2MzvAecZzJxGKO_RTvPM,319
numpy/distutils/checks/cpu_vx.c,sha256=v1UZMj78POCN7sbFmW6N0GM_qQSUwHxiF15LQYADIUs,477
numpy/distutils/checks/cpu_vxe.c,sha256=1w8AvS6x8s_zTgcrDEGMKQmSqpJRX2NLprdSu_ibyjk,813
numpy/distutils/checks/cpu_vxe2.c,sha256=fY9P2fWo-b08dy4dmuNNc_xX3E0ruPRU9zLPzzgD-Z8,645
numpy/distutils/checks/cpu_xop.c,sha256=sPhOvyT-mdlbf6RlbZvMrslRwHnTFgP-HXLjueS7nwU,246
numpy/distutils/checks/extra_avx512bw_mask.c,sha256=7IRO24mpcuXRhm3refGWP91sy0e6RmSkmUQCWyxy__0,654
numpy/distutils/checks/extra_avx512dq_mask.c,sha256=jFtOKEtZl3iTpfbmFNB-u4DQNXXBST2toKCpxFIjEa0,520
numpy/distutils/checks/extra_avx512f_reduce.c,sha256=hIcCLMm_aXPfrhzCsoFdQiryIrntPqfDxz0tNOR985w,1636
numpy/distutils/checks/extra_vsx4_mma.c,sha256=-Pz_qQ55WfWmTWGTH0hvKrFTU2S2kjsVBfIK3w5sciE,520
numpy/distutils/checks/extra_vsx_asm.c,sha256=anSZskhKZImNk0lsSJJY_8GJQ0h3dDrkrmrGitlS7Fw,981
numpy/distutils/checks/test_flags.c,sha256=7rgVefVOKOBaefG_6riau_tT2IqI4MFrbSMGNFnqUBQ,17
numpy/distutils/command/__init__.py,sha256=DCxnKqTLrauOD3Fc8b7qg9U3gV2k9SADevE_Q3H78ng,1073
numpy/distutils/command/autodist.py,sha256=i2ip0Zru8_AFx3lNQhlZfj6o_vg-RQ8yu1WNstcIYhE,3866
numpy/distutils/command/bdist_rpm.py,sha256=9uZfOzdHV0_PRUD8exNNwafc0qUqUjHuTDxQcZXLIbg,731
numpy/distutils/command/build.py,sha256=6IbYgycGcCRrrWENUBqzAEhgtUhCGLnXNVnTCu3hxWc,2675
numpy/distutils/command/build_clib.py,sha256=dU-pKIebuQGlBvp93y2t4tfJlW7Z64cBgUzofvmg_Hk,19703
numpy/distutils/command/build_ext.py,sha256=wQ6DuUpX6wvD98eRuvNiiFEDUfj58W3dvpycdh9uJBE,32610
numpy/distutils/command/build_py.py,sha256=xBHZCtx91GqucanjIBETPeXmR-gyUKPDyr1iMx1ARWE,1175
numpy/distutils/command/build_scripts.py,sha256=AEQLNmO2v5N-GXl4lwd8v_nHlrauBx9Y-UudDcdCs_A,1714
numpy/distutils/command/build_src.py,sha256=6hPzM9yEdLAs2U06qke9CRmrjbSRhNQ7yVj6kCBDtOw,31945
numpy/distutils/command/config.py,sha256=DVm5hmolBpTcLTiHQC_mf0nLndAqn2fc1GTo4ucHt3I,21241
numpy/distutils/command/config_compiler.py,sha256=I-xAL3JxaGFfpR4lg7g0tDdA_t7zCt-D4JtOACCP_Ak,4495
numpy/distutils/command/develop.py,sha256=5ro-Sudt8l58JpKvH9FauH6vIfYRv2ohHLz-9eHytbc,590
numpy/distutils/command/egg_info.py,sha256=n6trbjRfD1qWc_hRtMFkOJsg82BCiLvdl-NeXyuceGc,946
numpy/distutils/command/install.py,sha256=s_0Uf39tFoRLUBlkrRK4YlROZsLdkI-IsuiFFaiS3ls,3157
numpy/distutils/command/install_clib.py,sha256=q3yrfJY9EBaxOIYUQoiu2-juNKLKAKKfXC0nrd4t6z0,1439
numpy/distutils/command/install_data.py,sha256=r8EVbIaXyN3aOmRugT3kp_F4Z03PsVX2l_x4RjTOWU4,872
numpy/distutils/command/install_headers.py,sha256=g5Ag2H3j3dz-qSwWegxiZSAnvAf0thYYFwfPVHf9rxc,944
numpy/distutils/command/sdist.py,sha256=XQM39b-MMO08bfE3SJrrtDWwX0XVnzCZqfAoVuuaFuE,760
numpy/distutils/conv_template.py,sha256=hL0DDy7tMJ-5I-63BmkWkoLNX2c5GiQdQhj-XNG3Tm8,9865
numpy/distutils/core.py,sha256=4vvNzpLy_9AfakXgzC6OITRThJd4OdfSmrzxhYu49Fc,8388
numpy/distutils/cpuinfo.py,sha256=l5G7myXNwEOTynBIEitH-ghaF8Zw5pHQAjaYpPKNtTQ,23322
numpy/distutils/exec_command.py,sha256=pZJtbHqveE9tNL1UZixsYDAEB9oN-ocJFW5LjWHscMc,10659
numpy/distutils/extension.py,sha256=gho-x1rzPK16ca8zakRKHvbZL4Gvp1VFTEToE2-2k4M,3675
numpy/distutils/fcompiler/__init__.py,sha256=IBYxVrrcP2i-Z7tdMFwxCRnmrMKSwVkwfWWIFU0tRBQ,41177
numpy/distutils/fcompiler/absoft.py,sha256=4UKxvpWQIphSdi6vJb3qILML_zyM3K_m7ddhAMS5dBI,5655
numpy/distutils/fcompiler/arm.py,sha256=x_ym73nBEXIusMAFLKWB3_qWfEduHOr-9q656g0gzRo,2308
numpy/distutils/fcompiler/compaq.py,sha256=yyReqFAq42dy1zscMAV0GqVaYW7Iao1HtAUpnv5XTec,4023
numpy/distutils/fcompiler/environment.py,sha256=PVS1al3wahDNnneNVSl1sQhMPfz2dUXaIDVJfy0wZBU,3168
numpy/distutils/fcompiler/fujitsu.py,sha256=g4dTLDFfLRAzhYayIwyHGBw1Y36DKtPOCYfA823ldNA,1379
numpy/distutils/fcompiler/g95.py,sha256=1TJe4IynWYqqYBy8gJ-nz8WQ_TaSbv8k2UzUIY5Erqc,1372
numpy/distutils/fcompiler/gnu.py,sha256=-WOUQA-eHFsga5p22fK6-4Wsr0IgAAd_g_QLmKvbBBQ,20821
numpy/distutils/fcompiler/hpux.py,sha256=SLbDOPYgiixqE32GgUrAJjpDLFy9g7E01vGNZCGv6Pc,1394
numpy/distutils/fcompiler/ibm.py,sha256=HARaSCruJSuHbS3LruVESj2cndZUKTHiJZBn7NU4vo0,3636
numpy/distutils/fcompiler/intel.py,sha256=rlm017cVcyjIy1_s8a4lNHJ8ilo6TiYcIA_tuPojapY,6781
numpy/distutils/fcompiler/lahey.py,sha256=EV3Zhwq-iowWAu4BFBPv_UGJ-IB-qxlxmi6WU1qHDOs,1372
numpy/distutils/fcompiler/mips.py,sha256=mlUNgGrRSLnNhtxQXWVfC9l4_OP2GMvOkgbZQwBon0A,1768
numpy/distutils/fcompiler/nag.py,sha256=FpoDQWW_Y3Anm9-Psml-eNySCGzCp9_jP2Ej4_AwDy8,2864
numpy/distutils/fcompiler/none.py,sha256=auMK2ou1WtJ20LeMbwCZJ3XofpT9A0YYbMVd-62Mi_E,786
numpy/distutils/fcompiler/nv.py,sha256=jRyTlRE57lFJq659Xi-oUIy79nXYucyHawspR_D8c44,1613
numpy/distutils/fcompiler/pathf95.py,sha256=ipbaZIO8sqPJ1lUppOurnboiTwRzIasWNAJvKmktvv4,1094
numpy/distutils/fcompiler/pg.py,sha256=cVcSFM9oR0KmO5AIb4Odw9OGslW6zvDGP88n-uEwxvQ,3696
numpy/distutils/fcompiler/sun.py,sha256=JMdFfKldTYlfW1DxV7nR09k5PZypKLWpP7wmQzmlnH0,1628
numpy/distutils/fcompiler/vast.py,sha256=JUGP68JGOUOBS9WbXftE-qCVUD13fpLyPnhpHfTL5y0,1719
numpy/distutils/from_template.py,sha256=BL-vypfI0GNJrTo-nKs445liTW2Qdfvrsu8RMjATL5A,8174
numpy/distutils/intelccompiler.py,sha256=77BDCj7_6Nnf92ZDeFQgA6aDKJGkzDQ7u0nuQGw1v8g,4345
numpy/distutils/lib2def.py,sha256=HQ7i5FUtBcFGNlSlN20lgVtiBAHQbGXxmYdvkaJTjLI,3760
numpy/distutils/line_endings.py,sha256=hlI71r840mhfu8lmzdHPVZ4NFm-kJDDUMV3lETblVTY,2109
numpy/distutils/log.py,sha256=a5-sPwcZei7kSP0ZQZH4tTrlRWHnL8jtzLCeUSPA_04,2990
numpy/distutils/mingw/gfortran_vs2003_hack.c,sha256=FDTA53KYTIhil9ytvZlocOqghQVp9LacLHn1IurV0wI,83
numpy/distutils/mingw32ccompiler.py,sha256=igsvoJ-BYFxPBTWdCMeRQjINXZ10gzNTuAFPErl2UrA,22879
numpy/distutils/misc_util.py,sha256=InuiiXvHjM_WekXfdWT3QG9ESTmfiDtvprLLLWWJYAs,91876
numpy/distutils/msvc9compiler.py,sha256=bCtCVJmGrBHPm9sOoxa3oSrdrEVCNQFEM5O5hdqX8Hc,2255
numpy/distutils/msvccompiler.py,sha256=sGGkjB-iSQFEfsMfQY8ZJfPKs6vm2DY9Y_OKi0Fk__0,1986
numpy/distutils/npy_pkg_config.py,sha256=q-ASkO8wZ5HmiTEH_6gzO2bPV4i5dz3bTu4EuSxFQJM,13409
numpy/distutils/numpy_distribution.py,sha256=nrdp8rlyjEBBV1tzzi5cE-aYeXB5U3X8T5-G0akXSoY,651
numpy/distutils/pathccompiler.py,sha256=a5CYDXilCaIC85v0fVh-wrb0fClv0A7mPS87aF1inUc,734
numpy/distutils/setup.py,sha256=zd5_kD7uTEOTgC8Fe93g9A_5AOBEySWwr-2OxHsBBEc,651
numpy/distutils/system_info.py,sha256=TAicwq6Mi8zUJ44c9Q9hc_-VVPMiNyvpSwiMj0vIZKs,114200
numpy/distutils/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/distutils/tests/test_build_ext.py,sha256=te4h-jzABgSF_efL2LC-Hwc3hlq8lfqg5JEeHGQgV3A,2736
numpy/distutils/tests/test_ccompiler_opt.py,sha256=OQe2ldJSLY2Uvp7t91c45xs25CyeTWmqYDM5_h9a1wE,29559
numpy/distutils/tests/test_ccompiler_opt_conf.py,sha256=LmFqpGwqGFgUwLQdV5xJQhs_xB9gnL7_P-7oOOUqa78,6521
numpy/distutils/tests/test_exec_command.py,sha256=Ltd4T3A_t3Oo_QSYjOkWKqPj-LttXEumEVKhLxVfZEU,7515
numpy/distutils/tests/test_fcompiler.py,sha256=SS5HOLIg0eqkmZTRKeWq9_ahW2tmV9c9piwYfzcBPmc,1320
numpy/distutils/tests/test_fcompiler_gnu.py,sha256=RlRHZbyazgKGY17NmdYSF3ehO0M0xXN4UkbsJzJz4i8,2191
numpy/distutils/tests/test_fcompiler_intel.py,sha256=4cppjLugoa8P4bjzYdiPxmyCywmP9plXOkfsklhnYsQ,1088
numpy/distutils/tests/test_fcompiler_nagfor.py,sha256=ntyr8f-67dNI0OF_l6-aeTwu9wW-vnxpheqrc4cXAUI,1124
numpy/distutils/tests/test_from_template.py,sha256=ZzUSEPyZIG4Zak3-TFqmRGXHMp58aKTuLKb0t-5XpDg,1147
numpy/distutils/tests/test_log.py,sha256=ylfdL0kBkbjj_Tgqx47UGykAtpE_mJkLndL40p11AYc,902
numpy/distutils/tests/test_mingw32ccompiler.py,sha256=7X8V4hLMtsNj1pYoLkSSla04gJu66e87E_k-6ce3PrA,1651
numpy/distutils/tests/test_misc_util.py,sha256=YKK2WrJqVJ5o71mWL5oP0l-EVQmqKlf3XU8y7co0KYc,3300
numpy/distutils/tests/test_npy_pkg_config.py,sha256=1pQh-mApHjj0y9Ba2tqns79U8dsfDpJ9zcPdsa2qbps,2641
numpy/distutils/tests/test_shell_utils.py,sha256=okNSfjFSAvY3XyBsyZrKXAtV9RBmb7vX9o4ZLJc28Ds,2030
numpy/distutils/tests/test_system_info.py,sha256=MYZ2k5lQNg5l8hkZqwi13bSCZmeOPcgfjt3HIauwU-I,11320
numpy/distutils/unixccompiler.py,sha256=ED_e7yHVNj4oXMze6KY8TbPxjyvHDC6o4VNGAkFA5ZQ,5567
numpy/doc/__init__.py,sha256=llSbqjSXybPuXqt6WJFZhgYnscgYl4m1tUBy_LhfCE0,534
numpy/doc/constants.py,sha256=8jSZxoMlAwNxDbAdJQfiNvx5RgDDfp_ISjWKbpTqhsM,9567
numpy/doc/ufuncs.py,sha256=ERF8YNwda32wM_OH6-n56zECahjpH3bcGKv4gYA0txc,5494
numpy/dual.py,sha256=RgoFIabqn8Hu9lSRakjO1plfDdYBJQq_8Gn__rE8TqQ,2297
numpy/f2py/__init__.py,sha256=up110-9txrNl0FZQbkxQNb7If_xllGlqL1TmhBCCFf0,5476
numpy/f2py/__init__.pyi,sha256=nveiZP_Gx4OrS0EnsO6QNzdW70W3GaXaqGYf2VZItlY,1150
numpy/f2py/__main__.py,sha256=TDesy_2fDX-g27uJt4yXIXWzSor138R2t2V7HFHwqAk,135
numpy/f2py/__version__.py,sha256=TisKvgcg4vh5Fptw2GS1JB_3bAQsWZIKhclEX6ZcAho,35
numpy/f2py/auxfuncs.py,sha256=zzW7g7WGqp9d31Pei72VEaywe7zVBgIxagvTvKPA4Pk,22636
numpy/f2py/capi_maps.py,sha256=-WjijWJ197wQmalvOJvlXs3QHaPa9dmoIfFHpF5_WxE,32225
numpy/f2py/cb_rules.py,sha256=5N7kHBRihVXTFHwjwbLvBl_xfr6IFzuHWA9VzSdexVA,25494
numpy/f2py/cfuncs.py,sha256=zkxbGMy5gTLH7wxcNHgwaE5ASr3-v_LscU2nc2koUHE,50870
numpy/f2py/common_rules.py,sha256=pRpoVr457G3JcTD_W4V0AmgQBzAIKcqPw4j_Hv3elF0,5070
numpy/f2py/crackfortran.py,sha256=9hIRAzOef60nsox4AwnLqHMPklSRo70jWFJQdqEmQwQ,135378
numpy/f2py/diagnose.py,sha256=LmGu6iZKr9WHfMZFIIEobZvRJd9Ktdqx0nYekWk4N7g,5384
numpy/f2py/f2py2e.py,sha256=OlvH6CE1XdoRcWK86mF7CktkJnEwJcyOyj9xy3SUnww,25330
numpy/f2py/f90mod_rules.py,sha256=k-AxjnKtrMDFO4LYXSeEnYtlxFFtYysONHjTICuMka4,10081
numpy/f2py/func2subr.py,sha256=-e2VG2t0YDEOO_jIhLZRFLg5uoIoPGtcjir-4BzdNCs,9655
numpy/f2py/rules.py,sha256=5jWbkBAO-HfdMEluml8HF_lfKM3fkwj0U2MMsPkTJCE,63027
numpy/f2py/setup.py,sha256=AYA8aERgAo5gj7zt9IiYTnFK8qqjN2GzuxrfyuAJ_Qc,2406
numpy/f2py/src/fortranobject.c,sha256=vYA0Jv3GebldGmmm4mB1iBokAQv44v5S0-VU-Q9tklc,38732
numpy/f2py/src/fortranobject.h,sha256=cnO-2gqvn5BZfikyd2UtvZWB9hQ8tACMWu47iAtZxwM,4527
numpy/f2py/symbolic.py,sha256=bjBuZoVCaGmF1bQ2llJXAtbk-lslHn7Sf8wL9Lnedyc,54514
numpy/f2py/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/f2py/tests/src/abstract_interface/foo.f90,sha256=aCaFEqfXp79pVXnTFtjZBWUY_5pu8wsehp1dEauOkSE,692
numpy/f2py/tests/src/abstract_interface/gh18403_mod.f90,sha256=y3R2dDn0BUz-0bMggfT1jwXbhz_gniz7ONMpureEQew,111
numpy/f2py/tests/src/array_from_pyobj/wrapmodule.c,sha256=e1pCpcpHSLXdbGU8jVfe_P-y8fkzJuINpR1PyGXH10A,7473
numpy/f2py/tests/src/assumed_shape/.f2py_f2cmap,sha256=zfuOShmuotzcLIQDnVFaARwvM66iLrOYzpquIGDbiKU,30
numpy/f2py/tests/src/assumed_shape/foo_free.f90,sha256=fqbSr7VlKfVrBulFgQtQA9fQf0mQvVbLi94e4FTST3k,494
numpy/f2py/tests/src/assumed_shape/foo_mod.f90,sha256=9pbi88-uSNP5IwS49Kim982jDAuopo3tpEhg2SOU7no,540
numpy/f2py/tests/src/assumed_shape/foo_use.f90,sha256=9Cl1sdrihB8cCSsjoQGmOO8VRv9ni8Fjr0Aku1UdEWM,288
numpy/f2py/tests/src/assumed_shape/precision.f90,sha256=3L_F7n5ju9F0nxw95uBUaPeuiDOw6uHvB580eIj7bqI,134
numpy/f2py/tests/src/block_docstring/foo.f,sha256=KVTeqSFpI94ibYIVvUW6lOQ9T2Bx5UzZEayP8Maf2H0,103
numpy/f2py/tests/src/callback/foo.f,sha256=rLqaaaUpWFTaGVxNoGERtDKGCa5dLCTW5DglsFIx-wU,1316
numpy/f2py/tests/src/callback/gh17797.f90,sha256=-_NvQK0MzlSR72PSuUE1FeUzzsMBUcPKsbraHIF7O24,155
numpy/f2py/tests/src/callback/gh18335.f90,sha256=n_Rr99cI7iHBEPV3KGLEt0QKZtItEUKDdQkBt0GKKy4,523
numpy/f2py/tests/src/cli/hi77.f,sha256=bgBERF4EYxHlzJCvZCJOlEmUE1FIvipdmj4LjdmL_dE,74
numpy/f2py/tests/src/cli/hiworld.f90,sha256=RncaEqGWmsH9Z8BMV-UmOTUyo3-e9xOQGAmNgDv6SfY,54
numpy/f2py/tests/src/common/block.f,sha256=tcGKa42S-6bfA6fybpM0Su_xjysEVustkEJoF51o_pE,235
numpy/f2py/tests/src/crackfortran/accesstype.f90,sha256=3ONHb4ZNx0XISvp8fArnUwR1W9rzetLFILTiETPUd80,221
numpy/f2py/tests/src/crackfortran/foo_deps.f90,sha256=D9FT8Rx-mK2p8R6r4bWxxqgYhkXR6lNmPj2RXOseMpw,134
numpy/f2py/tests/src/crackfortran/gh15035.f,sha256=0G9bmfVafpuux4-ZgktYZ6ormwrWDTOhKMK4wmiSZlQ,391
numpy/f2py/tests/src/crackfortran/gh17859.f,sha256=acknjwoWYdA038oliYLjB4T1PHhXkKRLeJobIgB_Lbo,352
numpy/f2py/tests/src/crackfortran/gh2848.f90,sha256=-IpkeTz0j9_lkQeN9mT7w3U1cAJjQxSMdAmyHdF8oVg,295
numpy/f2py/tests/src/crackfortran/operators.f90,sha256=cb1JO2hIMCQejZO_UJWluBCP8LdXQbBJw2XN6YHB3JA,1233
numpy/f2py/tests/src/crackfortran/privatemod.f90,sha256=9O2oWEquIUcbDB1wIzNeae3hx4gvXAoYW5tGfBt3KWk,185
numpy/f2py/tests/src/crackfortran/publicmod.f90,sha256=nU_VXCKiniiUq_78KAWkXiN6oiMQh39emMxbgOVf9cg,177
numpy/f2py/tests/src/f2cmap/.f2py_f2cmap,sha256=fwszymaWhcWO296u5ThHW5yMAkFhB6EtHWqqpc9FAVI,83
numpy/f2py/tests/src/f2cmap/isoFortranEnvMap.f90,sha256=geH5qY3RmabRrjLG2WUpp2YNx99W7by9mVJBnpQRqwg,307
numpy/f2py/tests/src/kind/foo.f90,sha256=6_zq3OAWsuNJ5ftGTQAEynkHy-MnuLgBXmMIgbvL7yU,367
numpy/f2py/tests/src/mixed/foo.f,sha256=Zgn0xDhhzfas3HrzgVSxIL1lGEF2mFRVohrvXN1thU0,90
numpy/f2py/tests/src/mixed/foo_fixed.f90,sha256=6eEEYCH71gPp6lZ6e2afLrfS6F_fdP7GZDbgGJJ_6ns,187
numpy/f2py/tests/src/mixed/foo_free.f90,sha256=UC6iVRcm0-aVXAILE5jZhivoGQbKU-prqv59HTbxUJA,147
numpy/f2py/tests/src/module_data/mod.mod,sha256=EkjrU7NTZrOH68yKrz6C_eyJMSFSxGgC2yMQT9Zscek,412
numpy/f2py/tests/src/module_data/module_data_docstring.f90,sha256=-asnMH7vZMwVIeMU2YiLWgYCUUUxZgPTpbAomgWByHs,236
numpy/f2py/tests/src/negative_bounds/issue_20853.f90,sha256=IxBGWem-uv9eHgDhysEdGTmNKHR1gAiU7YJPo20eveM,164
numpy/f2py/tests/src/parameter/constant_both.f90,sha256=L0rG6-ClvHx7Qsch46BUXRi_oIEL0uw5dpRHdOUQuv0,1996
numpy/f2py/tests/src/parameter/constant_compound.f90,sha256=lAT76HcXGMgr1NfKof-RIX3W2P_ik1PPqkRdJ6EyBmM,484
numpy/f2py/tests/src/parameter/constant_integer.f90,sha256=42jROArrG7vIag9wFa_Rr5DBnnNvGsrEUgpPU14vfIo,634
numpy/f2py/tests/src/parameter/constant_non_compound.f90,sha256=u9MRf894Cw0MVlSOUbMSnFSHP4Icz7RBO21QfMkIl-Q,632
numpy/f2py/tests/src/parameter/constant_real.f90,sha256=QoPgKiHWrwI7w5ctYZugXWzaQsqSfGMO7Jskbg4CLTc,633
numpy/f2py/tests/src/quoted_character/foo.f,sha256=0zXQbdaqB9nB8R4LF07KDMFDbxlNdiJjVdR8Nb3nzIM,496
numpy/f2py/tests/src/regression/inout.f90,sha256=TlMxJjhjjiuLI--Tg2LshLnbfZpiKz37EpR_tPKKSx8,286
numpy/f2py/tests/src/return_character/foo77.f,sha256=tRyQSu9vNWtMRi7gjmMN-IZnS7ogr5YS0n38uax_Eo0,1025
numpy/f2py/tests/src/return_character/foo90.f90,sha256=WPQZC6CjXLbUYpzy5LItEoHmRDFxW0ABB3emRACsjZU,1296
numpy/f2py/tests/src/return_complex/foo77.f,sha256=7-iKoamJ-VObPFR-Tslhiw9E-ItIvankWMyxU5HqxII,1018
numpy/f2py/tests/src/return_complex/foo90.f90,sha256=_GOKOZeooWp3pEaTBrZNmPmkgGodj33pJnJmySnp7aE,1286
numpy/f2py/tests/src/return_integer/foo77.f,sha256=EKs1KeAOQBkIO99tMCx0H7_lpqvqpjie8zWZ6T_bAR4,1234
numpy/f2py/tests/src/return_integer/foo90.f90,sha256=0aYWcaAVs7Lw3Qbf8hupfLC8YavRuPZVIwjHecIlMOo,1590
numpy/f2py/tests/src/return_logical/foo77.f,sha256=Ax3tBVNAlxFtHhV8fziFcsTnoa8YJdapecMr6Qj7fLk,1244
numpy/f2py/tests/src/return_logical/foo90.f90,sha256=IZXCerFecYT24zTQ_spIoPr6n-fRncaM0tkTs8JqO1E,1590
numpy/f2py/tests/src/return_real/foo77.f,sha256=3nAY1YtzGk4osR2jZkHMVIUHxFoOtF1OLfWswpcV7kA,978
numpy/f2py/tests/src/return_real/foo90.f90,sha256=38ZCnBGWb9arlJdnVWvZjVk8uesrQN8wG2GrXGcSIJs,1242
numpy/f2py/tests/src/size/foo.f90,sha256=nK_767f1TtqVr-dMalNkXmcKbSbLCiabhRkxSDCzLz0,859
numpy/f2py/tests/src/string/char.f90,sha256=X_soOEV8cKsVZefi3iLT7ilHljjvJJ_i9VEHWOt0T9Y,647
numpy/f2py/tests/src/string/fixed_string.f90,sha256=tCN5sA6e7M1ViZtBNvTnO7_efk7BHIjyhFKBoLC3US0,729
numpy/f2py/tests/src/string/string.f,sha256=JCwLuH21Ltag5cw_9geIQQJ4Hv_39NqG8Dzbqj1eDKE,260
numpy/f2py/tests/test_abstract_interface.py,sha256=1tEd5Bswrc10NHUDBJPBOYVFHghIyzZ5MNru4E7Mofg,743
numpy/f2py/tests/test_array_from_pyobj.py,sha256=mex0sMGOzHGfnTTyBJTUo_Esfa5IT6pLGk3VHiNKWxM,22698
numpy/f2py/tests/test_assumed_shape.py,sha256=GB-Ka42fMzovex_GOtMvtIGYOnNqVwN_i2J705x5ojE,1494
numpy/f2py/tests/test_block_docstring.py,sha256=0UP4PhKCiF6vgaczLt3tHijL4sbP6DIn6NMTVfukkOI,581
numpy/f2py/tests/test_callback.py,sha256=Z21AIugJeU152SEGN782-HJnOqYBH2_KUL8UwG-nRHw,6315
numpy/f2py/tests/test_common.py,sha256=x_GnxoBXNgij-zsl875va_fUf38uSWj6JyDePM359wY,602
numpy/f2py/tests/test_compile_function.py,sha256=-i-zIVJzI-H3mmikdW9cINNmJ35JbuTTlFq5Ld9kswY,4303
numpy/f2py/tests/test_crackfortran.py,sha256=MKOczOI7j7wv4LNQIMXug8zAIqK-ynXtJBGCFEQKUFM,9167
numpy/f2py/tests/test_f2cmap.py,sha256=bqUqqq1DJz9k2v2TnE9YKN13TS2l8kL1Yxw87J4jpVk,406
numpy/f2py/tests/test_f2py2e.py,sha256=CtVdVsroPO03vqcGOe7nyvZjq-iFIk2r8GAgJ2NrNkA,20514
numpy/f2py/tests/test_kind.py,sha256=ShvLRQIimK1TLl1rkpSI3K7TDecWloq7-jZ-D1omEzs,873
numpy/f2py/tests/test_mixed.py,sha256=XmGaJC4Nf1OZfOxVX7TYp6KlaeZeb8ZytmiKIql3VvI,881
numpy/f2py/tests/test_module_doc.py,sha256=vYP-TM5D21z2igbKcR9WVNSOLSXQWyQ9yVa6l9W2k90,890
numpy/f2py/tests/test_parameter.py,sha256=E3SfDemPom95ZpBjBq59Z98ztghPf6biwLrU-kmc56M,4053
numpy/f2py/tests/test_quoted_character.py,sha256=0rwaANREvKwA0Rz65_B5S2xKy52kx7xzdijO5qFz4ac,470
numpy/f2py/tests/test_regression.py,sha256=A9TmLBcr9UMlA0Bj7bHq3FrblIgDZEBWBnyQIFbhgRo,2223
numpy/f2py/tests/test_return_character.py,sha256=Lyd6NoXRpDwWmUr__IJcs11EdCRU6fPDdzBLvXSlPF4,1536
numpy/f2py/tests/test_return_complex.py,sha256=ez0DwLDsrMqqCvrxRt3Le6uE2uP02wxp_mrki8xH7DE,2455
numpy/f2py/tests/test_return_integer.py,sha256=dxOnJ1h2JJsFVCsfUTKfk2vVaJGz-nXUYffCV46xY34,1905
numpy/f2py/tests/test_return_logical.py,sha256=DGZy3wzV-OL3JncBOovoHFAYNCxlSquUTK34jU6PwIw,2081
numpy/f2py/tests/test_return_real.py,sha256=FQ1k7WgRaq78pEw7rkb_CKgjqFqzl97iql4LzdQryzU,3455
numpy/f2py/tests/test_semicolon_split.py,sha256=XRRffHS0K82Kls91xtLcSzAIoWpHHpii8ZaUkJYSSH4,1709
numpy/f2py/tests/test_size.py,sha256=Qy8KH9Co1IL6GbnDJ5IDGRPD1HKQ3HL6zXCkN2wpuUY,1209
numpy/f2py/tests/test_string.py,sha256=vSMQKo1SK4Y1xpgVw8iquHHH2kmelFsmphMMKYhnAaM,3062
numpy/f2py/tests/test_symbolic.py,sha256=Zk4h3WC2etMrIEyMrayPpGthpWfuS35Yz-4XzzGFcY4,18835
numpy/f2py/tests/util.py,sha256=TpsqLMUBKKnNbNIzWSAto5DzWf256TEKYLjAsAHiYFI,10599
numpy/f2py/use_rules.py,sha256=ROzvjl0-GUOkT3kJS5KkYq8PsFxGjebA6uPq9-CyZEQ,3700
numpy/fft/__init__.py,sha256=efF5_sqvdBfwIBHcEvkIb0a7YWZvR5oa4jh_aelUTpk,8387
numpy/fft/__init__.pyi,sha256=wMurHg76HViKOj8Nt6YveOJ-zt_a4DOGuDu2TRmshvY,579
numpy/fft/_pocketfft.py,sha256=Eig0b3LbIFwSRFVcB6XHbBpAbDFIjO5gijB5_cY8_1o,54321
numpy/fft/_pocketfft.pyi,sha256=W-WyKPRKzA0JwHwaDhkJ7uMNeXxv1-n1-kvqnBdj94g,2479
numpy/fft/_pocketfft_internal.cp310-win_amd64.pyd,sha256=mSrCQt7BrxTBz34blqZlhrmWK-1wEfACXCNl8TFWBic,110592
numpy/fft/helper.py,sha256=divqfKoOb0p-Zojqokhj3fQ5HzgWTigxDxZIQnN_TUQ,6375
numpy/fft/helper.pyi,sha256=FN_saVsbmR1shO4xh0tm63CW7t_sv4w_8etoMwRGzMQ,1199
numpy/fft/setup.py,sha256=-aF3b5s_eL6Jl0rS_jMFtFPzbYyT55FQE2SgblAawf0,750
numpy/fft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/fft/tests/test_helper.py,sha256=iopdl7-SHA5E1Ex13rYdceskgoDI5T8_Kg12LfqK1HA,6315
numpy/fft/tests/test_pocketfft.py,sha256=ia4A2pJxy5Rohir3c47XlO3WiB9DWYIfFv4ueh5sVyg,13134
numpy/lib/__init__.py,sha256=D_APB98cfipadeVf9KXXCg4MRYwNl_JrjbrJ_Fityhc,1840
numpy/lib/__init__.pyi,sha256=s-DOCpQp4XmHBxq1BnMBc9f-bR7dgqem-xz8jRfNONs,5827
numpy/lib/_datasource.py,sha256=jK47KlC6O0l6Ev2la0HB0GfOgih94O8DsNjjeHa-ZRg,23347
numpy/lib/_iotools.py,sha256=IBMRLRtNoYbqUUPO9tite0VQYbMG8J7SN2ZCNj3pbQE,31770
numpy/lib/_version.py,sha256=IjsC8gQRuFo_Ns0fSuF0BW7ndA3h9jQV5mClyWe9A8s,5010
numpy/lib/_version.pyi,sha256=rw_2q4KJ-dfpqJuj1e3PtVqK4Yh2FdJa5AHdE5IRaWM,650
numpy/lib/arraypad.py,sha256=V4iW49CT3KyPHdVK-4Q2kjJqgEb-N35LusA6G-Q2LKU,32102
numpy/lib/arraypad.pyi,sha256=8z4FY3lJ1Xde2FHkoxmpz94aYDk5MDFoMvto8KALxvM,1813
numpy/lib/arraysetops.py,sha256=hAKq8W6JER0IKxyZPuSQx6sLY7ckiRkgq8Tp95qC_Sw,27567
numpy/lib/arraysetops.pyi,sha256=WIGeglI8_gOw4ExXRj15S1qN8QE1BUSDlEyoR98IEl0,8697
numpy/lib/arrayterator.py,sha256=29pO5S0ciEZwt1402Q0-5cRbyKspV4tlPX1-m_D_Hgc,7282
numpy/lib/arrayterator.pyi,sha256=GlJgmZGbY7vomeNHcX6jvmc5hVtguq4-fYFsw3zj2Zs,1586
numpy/lib/format.py,sha256=V-fWbIbTdxd_Bq7XetoW09_BjGHOvYCpgQI-XvGY0js,32293
numpy/lib/format.pyi,sha256=dAlF-kNz-H-Vtn9H7Cs8J1l1xUkc3A2e7oWK1Qy17bs,770
numpy/lib/function_base.py,sha256=YwccD6oy8xVwL8KZlOnk7MQ2B7DnSmMB5pWAaRA6M-k,188436
numpy/lib/function_base.pyi,sha256=6-bPdKIQb0kZ68j2uHo1SJJRmXooEX1AMrrcqvFuFZs,17322
numpy/lib/histograms.py,sha256=fIgBZ8fWwj0TL0wPOoVezQqnt6DxrW2-dqJGTtWoUEw,41341
numpy/lib/histograms.pyi,sha256=4NmqvpWIEpihimkX53XdtKv4TwWeJisG4sl-qPTSl0U,1099
numpy/lib/index_tricks.py,sha256=o4gdAQHNwoZiPeTI-ziTQeZqCp64TbVxjRh8yvs3tDE,31589
numpy/lib/index_tricks.pyi,sha256=Pk4fxh8vJ2peFeBMJHqW-BXMPPF5lw0xsqikzEvHqso,4403
numpy/lib/mixins.py,sha256=j_Hfrfp6d6lJAVFlM9y2lu6HHsK_yDInyU74SJWsZDQ,7228
numpy/lib/mixins.pyi,sha256=311dfgGno6FFV1knGjcJob3GqML80FxBaYOTZuYnC_A,3191
numpy/lib/nanfunctions.py,sha256=Ec8GqGL32CZ8DR6la2rbwfC6wFDSdAGlcr0OGfA024A,67552
numpy/lib/nanfunctions.pyi,sha256=_KUODwVSfoDYVb8Qp2FDnLfmusNM690WpSVuMrFwvw8,644
numpy/lib/npyio.py,sha256=UNRrRDCcvbLx4yu-5jpdII6dNo3SehCgS9yfaIhS8XY,97192
numpy/lib/npyio.pyi,sha256=TLkY7wEDZ8FqeTgb5oSh97oEVTB0NwU_b0FJfj5vscw,9943
numpy/lib/polynomial.py,sha256=n_VvTCSMEXtqe2acdsSPTY8jphjqPWpqIuy6I6TIOW4,45595
numpy/lib/polynomial.pyi,sha256=E-leT7vdwHnTkLa2CeQ4Ntnr6oNgVy89sUXQOW50qlU,7261
numpy/lib/recfunctions.py,sha256=pJkJYhkttpQMTyZysnxCSgRrm-Jez1Hi9fV3Cx3-hoU,57915
numpy/lib/scimath.py,sha256=mVVANvK9ChJ3myB9ktfnOrh936ilNey9Eq8StSOYzbc,15662
numpy/lib/scimath.pyi,sha256=bcW3wCbYG_cQpWyMAQ9dRY5JenhnGt8RiBjCTewaxag,2977
numpy/lib/setup.py,sha256=1s3corE4egZnDZ6I8au_mx7muRPgb3ZxL4Ko2SHt_2Q,417
numpy/lib/shape_base.py,sha256=VuBrY9VgA7IvQF4BQnDqLujCadihjqPhsFeZv62P-f0,40547
numpy/lib/shape_base.pyi,sha256=Qlc5aMrGspeHpvGB6Fxim7Hqos3EXG7vhrrzEocAv98,5399
numpy/lib/stride_tricks.py,sha256=fVtBjoR_zpOKBYsGkFPH8QtKBzpTo3kVi-01HW5tMzg,18458
numpy/lib/stride_tricks.pyi,sha256=papM2ENge9VNzvNApZ3a2VApgrIkytrzr_Oy-ia0iwM,1827
numpy/lib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/lib/tests/data/py2-objarr.npy,sha256=F4cyUC-_TB9QSFLAo2c7c44rC6NUYIgrfGx9PqWPSKk,258
numpy/lib/tests/data/py2-objarr.npz,sha256=xo13HBT0FbFZ2qvZz0LWGDb3SuQASSaXh7rKfVcJjx4,366
numpy/lib/tests/data/py3-objarr.npy,sha256=pTTVh8ezp-lwAK3fkgvdKU8Arp5NMKznVD-M6Ex_uA0,341
numpy/lib/tests/data/py3-objarr.npz,sha256=qQR0gS57e9ta16d_vCQjaaKM74gPdlwCPkp55P-qrdw,449
numpy/lib/tests/data/python3.npy,sha256=X0ad3hAaLGXig9LtSHAo-BgOvLlFfPYMnZuVIxRmj-0,96
numpy/lib/tests/data/win64python2.npy,sha256=agOcgHVYFJrV-nrRJDbGnUnF4ZTPYXuSeF-Mtg7GMpc,96
numpy/lib/tests/test__datasource.py,sha256=QUI-8kXh4rHVSC-G10dlflsonKudk3_hqi3CNRySMWM,10837
numpy/lib/tests/test__iotools.py,sha256=q44VFSi9VzWaf_dJ-MGBtYA7z7TFR0j0AF-rbzhLXoo,14096
numpy/lib/tests/test__version.py,sha256=v2TOlH4f1Pmzxn1HWby3eBgLO9tGnhwH2LvBXlXtHP4,2063
numpy/lib/tests/test_arraypad.py,sha256=yWarXsQkYnsgUmAvaMTVA4Gh4WTWJfW5jsWhJxYNQMs,55642
numpy/lib/tests/test_arraysetops.py,sha256=pN9xmH3UPVZO3d8iX4mke0-n7A3276zMkP76vPiv_iI,29522
numpy/lib/tests/test_arrayterator.py,sha256=IRVmzxbr9idboJjOHKuX_8NQhMAKs7pD1xWqmU3ZERw,1337
numpy/lib/tests/test_financial_expired.py,sha256=D4d0h5E49b8wfIRr_mYdh_VfSU1cFS8VWy5NHc7nBYM,258
numpy/lib/tests/test_format.py,sha256=oA5LN2Ofr7aB9d8NddEI7iCHTcvrgTfdPtJE1Ld4PiE,39424
numpy/lib/tests/test_function_base.py,sha256=taBdv_Rp3Tnrd1n9F3deGAAl-tjxzZqSyuBLMcNTyF0,149904
numpy/lib/tests/test_histograms.py,sha256=_uKM8dtMWCwGtIgIwog3s3jyPb8w804TWqy9iOkYeSI,34510
numpy/lib/tests/test_index_tricks.py,sha256=xOW7cRn9so6sybrMIY_Hx-r-X9CAYA-xPO7PpJggNL0,19472
numpy/lib/tests/test_io.py,sha256=tOqn54K04KlR_ADQUAfChIaLhoKfGmhj8oBCrD8LBow,109102
numpy/lib/tests/test_loadtxt.py,sha256=hEIbUAvO7JNpesZqjtkJRtoRrjxOkDiV0VTbcpESIYQ,38417
numpy/lib/tests/test_mixins.py,sha256=nIec_DZIDx7ONnlpq_Y2TLkIULAPvQ7LPqtMwEHuV4U,7246
numpy/lib/tests/test_nanfunctions.py,sha256=5ZgW1rWkCeqH9z69Cb4xbgiUr-gA8u1eZ2UMQ-O7G1g,45777
numpy/lib/tests/test_packbits.py,sha256=XpFIaL8mOWfzD3WQaxd6WrDFWh4Mc51EPXIOqxt3wS0,17922
numpy/lib/tests/test_polynomial.py,sha256=x2h1teRr7d-gcu17w0s3G4NlZt1jgOjKTiXP_7lPqVI,11698
numpy/lib/tests/test_recfunctions.py,sha256=WbFvOSi8XS2b1KEl_xA-GlD1Fk9PNPTsBqimlNPdM-w,42077
numpy/lib/tests/test_regression.py,sha256=NXKpFka0DPE7z0-DID-FGh0ITFXi8nEj6Pg_-uBcDIg,8504
numpy/lib/tests/test_shape_base.py,sha256=ZYu94kgr4KNzEdZ57RNpxmvSSO1kX5vwZg0oRJaluSc,26497
numpy/lib/tests/test_stride_tricks.py,sha256=1zeBcvhltePbeE6SBoF4gomAYaZzwaHjvbWqcgI2JiY,23494
numpy/lib/tests/test_twodim_base.py,sha256=aYXmpm4syhD1I44-hQPLFLDcQovF1BcydK6Y2DE1JN8,19499
numpy/lib/tests/test_type_check.py,sha256=ffuA-ndaMsUb0IvPalBMwGkoukIP9OZVJXCuq299qB8,15597
numpy/lib/tests/test_ufunclike.py,sha256=rpZTDbKPBVg5-byY1SKhkukyxB2Lvonw15ZARPE3mc0,3382
numpy/lib/tests/test_utils.py,sha256=C5Y3YQQ6wIWNklf4Mfp-Sfx6RL60nvgjJF1ne_XAPzA,4734
numpy/lib/twodim_base.py,sha256=SpRSDNex01XLhjQdeqJZKJLMuE4Qe7UKMevFdxgEZz8,32745
numpy/lib/twodim_base.pyi,sha256=7dmw1lzG8UvDxsRJiD9Pu_t45CjzcxtI-TZ0w5xUG1o,5705
numpy/lib/type_check.py,sha256=rWqKCYs3r3QyBd0x5eD0IRHHAj4KpDcee1FI6kcu6Q8,20666
numpy/lib/type_check.pyi,sha256=btZZOeVEcX7J7igi9d4bjgToV2CWoWPIl0ZDX9J84WU,5793
numpy/lib/ufunclike.py,sha256=snevUCqlqCQKAlzz5f8DdFXkAarEPafG8CHmzXMs-OI,8299
numpy/lib/ufunclike.pyi,sha256=iTi6kfrbWzcrKfAfChC7CY48U8CDVxghnb8wofcbpuw,1359
numpy/lib/user_array.py,sha256=5yqkyjCmUIASGNx2bt7_ZMWJQJszkbD1Kn06qqv7POA,8007
numpy/lib/utils.py,sha256=9_wSAEk1Rp3Ej-6Wyj2zJFePQS-x2nr_AIlF8ZVDo2g,34357
numpy/lib/utils.pyi,sha256=WznRQNQY_Im547hTCUO2QK3L0fPKY8hrwTm0Q7ij2Fg,2416
numpy/linalg/__init__.py,sha256=EefgbxHkx8sLgXXLtpD8i8A_SCx-V4uz1Z_aQkmW6Ec,1893
numpy/linalg/__init__.pyi,sha256=94TdIMqJcc1OMeDpom2Zv-QxKL81BQ55Otcc-wyOUeo,650
numpy/linalg/_umath_linalg.cp310-win_amd64.pyd,sha256=sFhmOo53cTusc0BO3xsbxCO9FccD1eX_wF6AHh0Q12Q,102400
numpy/linalg/lapack_lite.cp310-win_amd64.pyd,sha256=z9FOnf4urekq4-v5x_cETJOJlUjP3uL7AsafGktI8OI,17408
numpy/linalg/linalg.py,sha256=sISfzSPmP-IsAy-PDIy3HFK3zBKx6tQdZEJ6p7ZmZ5Y,92623
numpy/linalg/linalg.pyi,sha256=wdXH5Nj0iFpelmYBu8ixLpXef-q5A5Y38DFLY-a51Zc,7722
numpy/linalg/setup.py,sha256=md8OSGQe0MvVX2_XDcP06HEEWfxQvIdJa1vVh6-MC5o,3009
numpy/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/linalg/tests/test_deprecations.py,sha256=GaeE3JnQlJLoAfbY93LmgCFUlV5M8IFmQ7EhF4WbqwU,660
numpy/linalg/tests/test_linalg.py,sha256=MCube3uSlHO-niqMzAjwBgIwnqteQlzeyr7lJVS8ag8,79535
numpy/linalg/tests/test_regression.py,sha256=T0iQkRUhOoxIHHro5kyxU7GFRhN3pZov6UaJGXxtvu0,5745
numpy/ma/__init__.py,sha256=9i-au2uOZ_K9q2t9Ezc9nEAS74Y4TXQZMoP9601UitU,1458
numpy/ma/__init__.pyi,sha256=7-pSI7xHlMWrvMS_fKT5M683a8S9iqL7d6qYuYme8TQ,6320
numpy/ma/bench.py,sha256=4HgDHbz_nCW4GmVLyjqk5FcgU7O_cqxNyCHufLwyyIY,4989
numpy/ma/core.py,sha256=ep62g2TCLsGV336CxiQXS3_ZpHuOOfJB92Ybdha5QVo,277442
numpy/ma/core.pyi,sha256=iQYJgwktBViKfzulTTqBL6aiWhwNl6h9_DRIWgFyOls,14650
numpy/ma/extras.py,sha256=Xvnw7VWgHCvOpUihOz4RWRzn1YTN9rfYf228WgB3z7A,62918
numpy/ma/extras.pyi,sha256=C_OGRvFlzoEsfwX6SjhaIWLqhsJgljhzRkkWy5D9mko,2731
numpy/ma/mrecords.py,sha256=zy-LVXMJnyDCbsBfWyFCC8Z2-y6ApeXw9OfQJNiiWZg,28015
numpy/ma/mrecords.pyi,sha256=nMx2BRyVzU_7AnAKrF3QoBwQH9TxxQYqBrrv6WhVI_I,2024
numpy/ma/setup.py,sha256=DCi5FtZTlkhR3ByJH5Sp5B962bfcWpaOjA-y7ueyoog,430
numpy/ma/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/ma/tests/test_core.py,sha256=isXN3I5I8y6OuLTQSAM4pTfXMPGBqw2aRwgReHpTdfk,210931
numpy/ma/tests/test_deprecations.py,sha256=4f62l6k8ICMMQNLWa9KFuvVG0NsYwoGpR2q5DxdAoPo,2866
numpy/ma/tests/test_extras.py,sha256=QfmReRo7Mq3wvV2LF5Zp1vxEJl7-8qGs8VhKAWZI-N0,73320
numpy/ma/tests/test_mrecords.py,sha256=KPuJanNCTIvPXPyXYR3Yp7LGuP2tkPmbc1iXB9Lt5zU,20376
numpy/ma/tests/test_old_ma.py,sha256=kvhUKtjeGU_mi-NAuTm_Z0Z9vRNR2sS0XDh_ddcagD4,33632
numpy/ma/tests/test_regression.py,sha256=Hi-p5QdcivsxUDSpTl3MGtAnmJCJPhhIj3c5nYI9rw8,3170
numpy/ma/tests/test_subclassing.py,sha256=2Sa_sS5bsyLtS5FcbqYdCD0DymVvGktuWWBG9sXNYSc,14600
numpy/ma/testutils.py,sha256=5nMEDNCI9e5KWPGQqFhi2_HPHJO5mZ_XQaMXwE4Fhl4,10527
numpy/ma/timer_comparison.py,sha256=xhRDTkkqvVLvB5HeFKIQqGuicRerabKKX3VmBUGc4Zs,16101
numpy/matlib.py,sha256=l-292Lvk_yUCK5Y_U9u1Xa8grW8Ss0uh23o8kj-Hhd8,10741
numpy/matrixlib/__init__.py,sha256=OYwN1yrpX0doHcXpzdRm2moAUO8BCmgufnmd6DS43pI,228
numpy/matrixlib/__init__.pyi,sha256=w70nB0WHow4AVG5tw3Rl9zv4S2n63-68T8AhVJlNup0,267
numpy/matrixlib/defmatrix.py,sha256=zDXeDEu5DcW3R5ijl_MjGzp9bXM36uhWm3s0hbMTzJc,31780
numpy/matrixlib/defmatrix.pyi,sha256=i7medmOD8aL6_PMJSiGSnWmld_YOxsoP67Kh-SR_QLo,467
numpy/matrixlib/setup.py,sha256=DEY5vWe-ReFP31junY0nZ7HwDpRIVuHLUtiTXL_Kr3A,438
numpy/matrixlib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/matrixlib/tests/test_defmatrix.py,sha256=nbY_HkwzoJbhYhACiEN-cZmR644sVJvMKWUcsANPayQ,15435
numpy/matrixlib/tests/test_interaction.py,sha256=C1YtIubO6Qh8RR-XONzo8Mle4bu4SvwsvBnB0x0Gy4g,12229
numpy/matrixlib/tests/test_masked_matrix.py,sha256=aKC-imVpBSyjnokr0Ntn-e47P1MXhKzOSnGz1ji9J_c,9156
numpy/matrixlib/tests/test_matrix_linalg.py,sha256=9S9Zrk8PMLfEEo9wBx5LyrV_TbXhI6r-Hc5t594lQFY,2152
numpy/matrixlib/tests/test_multiarray.py,sha256=E5jvWX9ypWYNHH7iqAW3xz3tMrEV-oNgjN3_oPzZzws,570
numpy/matrixlib/tests/test_numeric.py,sha256=l-LFBKPoP3_O1iea23MmaACBLx_tSSdPcUBBRTiTbzk,458
numpy/matrixlib/tests/test_regression.py,sha256=FgYV3hwkpO0qyshDzG7n1JfQ-kKwnSZnA68jJHS7TeM,958
numpy/polynomial/__init__.py,sha256=rtJL1VpB8PVOJSwKUpUJRYLdvZ4bkMnWNijL1ByTTWo,6973
numpy/polynomial/__init__.pyi,sha256=ng3gyC_49Isv2zjbUFrZOxRd0zltgfqz0EXXS2NLN8o,723
numpy/polynomial/_polybase.py,sha256=KOclxhhzXpqEXV3q_kc-pSeC5vPMjpdhAC9DBQ_galY,37626
numpy/polynomial/_polybase.pyi,sha256=VO8idl7jzsS6M5GenTZVTFPR74iKBnqqFO-r7WOC9FM,2316
numpy/polynomial/chebyshev.py,sha256=u0_qj8Nh0hzMK5nXD7os-G0nrIVazN7lmUgorTiXKJM,64565
numpy/polynomial/chebyshev.pyi,sha256=UKyHeff6dp8DV3J5e4-iT31lu4-pwIXSu_oy35AsQRU,1438
numpy/polynomial/hermite.py,sha256=HzyaK2GkgnbITmOHdMSLhA0ZL8EWnMU9btPG0aCWUzk,53935
numpy/polynomial/hermite.pyi,sha256=kpbjKXoOW1QSHFtPTxLx7LGtIY540CcpaBF_YTO1TVY,1263
numpy/polynomial/hermite_e.py,sha256=Lq36k03od-OhWsVq0fmfWi2TmTxQ_QGrTlmrQ4S-MS8,54055
numpy/polynomial/hermite_e.pyi,sha256=AP6dm9PsnEox2i5MyVbJRZQMd0mgaqw5IasrCFtPOoo,1284
numpy/polynomial/laguerre.py,sha256=G2Rzb69oy5cUUFpajmORr_gb6Kq8sy54mW3xtngodes,52213
numpy/polynomial/laguerre.pyi,sha256=v5RTMhLIRsGxx9vKAAs6WyQqBjxjNK-YrTFKEJcJFaw,1224
numpy/polynomial/legendre.py,sha256=fpjKkDF8Dr1ZYli-AV_nzYsUanA3qGw9N99UfOKbbVQ,52932
numpy/polynomial/legendre.pyi,sha256=IOzJVF0pQefwXsuBNhE7h5Ecib5SxKBJJ-Au2T4laU8,1224
numpy/polynomial/polynomial.py,sha256=suGZ4Ng8-JP0IGu6eAznkyhmBiLlakTsU8QrK0mh3iw,50220
numpy/polynomial/polynomial.pyi,sha256=w4ocQ2Wf3wQWHSO2gklfPS1JVq-56Q_XUCxLLcjIces,1173
numpy/polynomial/polyutils.py,sha256=goXNYkGla4D-kp7YH7YV_eDvDguKJ74tQuYKtUEmgko,22855
numpy/polynomial/polyutils.pyi,sha256=G_PMz2ZYuaxqE28s1zVAGrxiPXL2HZhOFjAQPMyb-1k,237
numpy/polynomial/setup.py,sha256=3MP1VT1AVy8_MdlhErP-lS0Rq5fActYCkxYeHJU88Vg,383
numpy/polynomial/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/polynomial/tests/test_chebyshev.py,sha256=PI2XwvGGqQKEB1RxbsYRgeTG0cunB_8Otd9SBJozq-8,21141
numpy/polynomial/tests/test_classes.py,sha256=TIoMhOcCJd7bAAs-LVNUToLFtYORWRU1H5CSwaPdySU,18931
numpy/polynomial/tests/test_hermite.py,sha256=zGYN24ia2xx4IH16D6sfAxIipnZrGrIe7D8QMJZPw4Y,19132
numpy/polynomial/tests/test_hermite_e.py,sha256=5ZBtGi2gkeldYVSh8xlQOLUDW6fcT4YdZiTrB6AaGJU,19467
numpy/polynomial/tests/test_laguerre.py,sha256=hBgo8w_3iEQosX2CqjTkUstTiuTPLZmfQNQtyKudZLo,18048
numpy/polynomial/tests/test_legendre.py,sha256=v3ajjp0sg1o7njoLhbPftxaIWaxpY0pBp1suImZqJMw,19241
numpy/polynomial/tests/test_polynomial.py,sha256=i7CaTnxfuLkVZzByFTvOVQo7HeMdQpGxgHRmHCFMTlw,20838
numpy/polynomial/tests/test_polyutils.py,sha256=AQZZzxBCymhT1MTv8zCF_NC-nP0d9ybMNebT2d8P3j0,3700
numpy/polynomial/tests/test_printing.py,sha256=o0D4bNFYlFo0YpPYMB6RZwH5zEFpE6qjdeZ-j-D0YsI,16176
numpy/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/__init__.pxd,sha256=g3EaMi3yfmnqT-KEWj0cp6SWIxVN9ChFjEYXGOfOifE,445
numpy/random/__init__.py,sha256=W_hFzGsKVQfdh3-U15gzsOKKAk8uZgioDkxKyuou4WA,7721
numpy/random/__init__.pyi,sha256=_b7IK-fEE4b2bfywzWGwBCkeZeKpyYF_2mfC22kb1yI,2125
numpy/random/_bounded_integers.cp310-win_amd64.pyd,sha256=9RRL2fF4c4J4BiDeUuRtsbbvacVHUDYsEhuoCIitbes,212992
numpy/random/_bounded_integers.pxd,sha256=ugYlh8FvGggHCjEaqgO4S_MeRcZg3mw40sDYEqx07QQ,1698
numpy/random/_common.cp310-win_amd64.pyd,sha256=TNXLLRLdUrzZ16w_4qK1e9rjXW_tMABLJy6LEiP3fEY,157696
numpy/random/_common.pxd,sha256=2ExIWSBchefLVZ7kQK1X_EOrlVhui5b41CTJfs0zk3s,4851
numpy/random/_examples/cffi/extending.py,sha256=BgydYEYBb6hDghMF-KQFVc8ssUU1F5Dg-3GyeilT3Vg,920
numpy/random/_examples/cffi/parse.py,sha256=Jb5SRj8As2P07PtoeuiZHZctaY0oAb065PXbG4-T8fI,1884
numpy/random/_examples/cython/extending.pyx,sha256=RmpxvFfGsAGZwCY78LWrfpa307NG7vrE64TIiIpKEA4,2368
numpy/random/_examples/cython/extending_distributions.pyx,sha256=1zrMvPbKi0RinyZ93Syyy4OXGEOzAAKHSzTmDtN09ZY,3987
numpy/random/_examples/cython/setup.py,sha256=dnSUyiRdWWaMfhuNYllvSc8JUrUIknXXWE2CMYDAA50,1491
numpy/random/_examples/numba/extending.py,sha256=vnqUqQRvlAI-3VYDzIxSQDlb-smBAyj8fA1-M2IrOQw,2041
numpy/random/_examples/numba/extending_distributions.py,sha256=tU62JEW13VyNuBPhSpDWqd9W9ammHJCLv61apg90lMc,2101
numpy/random/_generator.cp310-win_amd64.pyd,sha256=caXn7ueJ50beJsIJi57H6VfAmRo9uNKYQOWH_NtUPaA,635392
numpy/random/_generator.pyi,sha256=RH2CT75_ONncmv1FbT_0t21fZBAJjZovn0yfqLrNUdQ,22320
numpy/random/_mt19937.cp310-win_amd64.pyd,sha256=O0rUTQldgHAo1Wl4iuYAzN4fiPWlCCcnSm5uZVX63Rc,67072
numpy/random/_mt19937.pyi,sha256=moJQemI0KlSzUyLJKy4d3lq6-yzWe-eKe0-ocybBjmY,746
numpy/random/_pcg64.cp310-win_amd64.pyd,sha256=G0ZqJsozTgkQIwBYXDPf_vFXa5quwkUodotlazKKNqE,70656
numpy/random/_pcg64.pyi,sha256=Q-QetvAEmjzguUzTFe9WyNjouYT4AdB3t4TP7Rv_h9A,1133
numpy/random/_philox.cp310-win_amd64.pyd,sha256=yzv3t8fBqmf5RJB8MbSZlPSfEQDvjyQVqlQzU7T7sw8,58368
numpy/random/_philox.pyi,sha256=ON7UZsb8vsIHC8LnnEAe_pe5RNvs8AxY6QoyuGshArc,1014
numpy/random/_pickle.py,sha256=SKrevDkN5zlFIWm1MEbsbhY7bpfw3wlG30BuXo7AeKc,2388
numpy/random/_sfc64.cp310-win_amd64.pyd,sha256=AsxK3voabnAQkQZrfcDufrF8pS6RN1kqheIw0ZnZXro,41984
numpy/random/_sfc64.pyi,sha256=P4SSo1zmzgVONfY79sLS0fASLZJ4hMwFz97akUzXLzs,737
numpy/random/bit_generator.cp310-win_amd64.pyd,sha256=kjT45vtH5ijPumYrlQu7X221DAxd874JnXkmNAlhDi4,135680
numpy/random/bit_generator.pxd,sha256=LJpeB-EKeVV8_JO69sS33XJLZQ3DAhrUCNzs_ei7AoI,1042
numpy/random/bit_generator.pyi,sha256=ihfaoTX_uPGKtJDhPljMC4y5Nr98egq7HeAu9Xg7wn8,3496
numpy/random/c_distributions.pxd,sha256=N8O_29MDGDOyMqmSKoaoooo4OJRRerBwsBXLw7_4j54,6147
numpy/random/lib/npyrandom.lib,sha256=eJbFB338D7eqMYxiLDUufScdI-qmq53RApU3M3lrSXA,143948
numpy/random/mtrand.cp310-win_amd64.pyd,sha256=hH5yhA5Jzx47cB2ERwym-m6IP3OeRZc4bK5DI-QBJus,541696
numpy/random/mtrand.pyi,sha256=jYezPo8VLaY0yJ2rnVcsTY0uDMeiWAohL9j0drQWHLQ,20180
numpy/random/setup.py,sha256=lbE6XMpWreKmU5lyBvFnwPKfFmg8Xfu5JK-Ki50GvsE,7167
numpy/random/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/tests/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/tests/data/mt19937-testset-1.csv,sha256=bA5uuOXgLpkAwJjfV8oUePg3-eyaH4-gKe8AMcl2Xn0,16845
numpy/random/tests/data/mt19937-testset-2.csv,sha256=SnOL1nyRbblYlC254PBUSc37NguV5xN-0W_B32IxDGE,16826
numpy/random/tests/data/pcg64-testset-1.csv,sha256=wHoS7fIR3hMEdta7MtJ8EpIWX-Bw1yfSaVxiC15vxVs,24840
numpy/random/tests/data/pcg64-testset-2.csv,sha256=6vlnVuW_4i6LEsVn6b40HjcBWWjoX5lboSCBDpDrzFs,24846
numpy/random/tests/data/pcg64dxsm-testset-1.csv,sha256=Fhha5-jrCmRk__rsvx6CbDFZ7EPc8BOPDTh-myZLkhM,24834
numpy/random/tests/data/pcg64dxsm-testset-2.csv,sha256=mNYzkCh0NMt1VvTrN08BbkpAbfkFxztNcsofgeW_0ns,24840
numpy/random/tests/data/philox-testset-1.csv,sha256=QvpTynWHQjqTz3P2MPvtMLdg2VnM6TGTpXgp-_LeJ5g,24853
numpy/random/tests/data/philox-testset-2.csv,sha256=-BNO1OCYtDIjnN5Q-AsQezBCGmVJUIs3qAMyj8SNtsA,24839
numpy/random/tests/data/sfc64-testset-1.csv,sha256=sgkemW0lbKJ2wh1sBj6CfmXwFYTqfAk152P0r8emO38,24841
numpy/random/tests/data/sfc64-testset-2.csv,sha256=mkp21SG8eCqsfNyQZdmiV41-xKcsV8eutT7rVnVEG50,24834
numpy/random/tests/test_direct.py,sha256=NLlyQC2gDpSmJ2Vj_rvrKZImvDkZOYVNvtx-Rdb4LVk,16907
numpy/random/tests/test_extending.py,sha256=7ufuv2bo2LosYNBlTIAFGjLnt9WUg02ziuYtJ1jx16U,3583
numpy/random/tests/test_generator_mt19937.py,sha256=iW_-ac-POCJqvpa228HAvhMYvDlJLCNtB6fMNpx73SM,116020
numpy/random/tests/test_generator_mt19937_regressions.py,sha256=YVgW77VNLSG36rf7v2_BitcRGooHRKr5syd_PcOjkDo,5789
numpy/random/tests/test_random.py,sha256=ocJfbjj9gupfY4eioM6jxb4z5YFCqtBDpzGtG2LOFfo,71698
numpy/random/tests/test_randomstate.py,sha256=tnozs-Dhi-g5kIo5C5aK1Um-bbhwNRtnzlPiGZVHOXo,83538
numpy/random/tests/test_randomstate_regression.py,sha256=MgTKKmJlf_U9I_rmCRpIvPjPsq9ZMW1qGomnIcWr6cw,8133
numpy/random/tests/test_regression.py,sha256=QZc3xP9sPfihkUF7LAzW8zE4AaJ0nionJoYO3M6pAdk,5588
numpy/random/tests/test_seed_sequence.py,sha256=zWUvhWDxBmTN2WteSFQeJ29W0-2k3ZUze_3YtL4Kgms,3391
numpy/random/tests/test_smoke.py,sha256=KggwK88hgKJyDscYQaODUCS7jLEk1QE9JW-jJRmvxig,29001
numpy/setup.py,sha256=LNAu_SzoMgaXtXCagj9GvCpT4mPnBlgXmgxJQ-P_WhU,1133
numpy/testing/__init__.py,sha256=RfaUXHdtdcM6LbtS_U2wInkR-0uXE_kpx9mVkfKSBCM,671
numpy/testing/__init__.pyi,sha256=3zza7ae_vF-ayV-AJRRqYD0q0VC2CWOB1I10l4Nscj0,1859
numpy/testing/_private/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/testing/_private/decorators.py,sha256=lKeZylqjrMV3zA_3IjBHX64mHNZ2Qo9PNFBeIQyzns4,11732
numpy/testing/_private/extbuild.py,sha256=2_qtacQAr9Wcqtypx5Isl4r7HdCUNn_c9EVO4XwpbZY,8067
numpy/testing/_private/noseclasses.py,sha256=OqMSWVZEg5GGgz8bsoDWKa3oxvXYoqPst6U423s8yBM,14880
numpy/testing/_private/nosetester.py,sha256=35S7suLWVzYBZ-zOt3ugthNZcMBCP7AjA-Z-4jqmtus,19980
numpy/testing/_private/parameterized.py,sha256=IDHScV_r5nxv6InuuXT9qfH_OuNhRcQUZ6DbuU3VYdI,16588
numpy/testing/_private/utils.py,sha256=gtRkPzBL7gMH6i4gOqocnNj9JvvbY8CWnYNSMeqsXiU,87951
numpy/testing/_private/utils.pyi,sha256=a4w42OC69aQu2kGoTp_Oxj6efunrRUI_4OvVBNZJZt8,10383
numpy/testing/print_coercion_tables.py,sha256=1fOhWxSwwHCF6QrX76PCjfHguignkTKCyvpRTq4Gcwo,6380
numpy/testing/setup.py,sha256=wQPOZ8LI1xqkSe7vfY0apJHJwEyJ490uSveFh1wmTuk,730
numpy/testing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/testing/tests/test_doctesting.py,sha256=wUauOPx75yuJgIHNWlPCpF0EUIGKDI-nzlImCwGeYo0,1404
numpy/testing/tests/test_utils.py,sha256=a0yichNUggLkAfvlsc8ipo5uMjSikaCUq5ATVan7S7A,56638
numpy/testing/utils.py,sha256=dJcf8XDd5szqpHy8tbPmXM43E4a1FQdc6HK-Lj_IQzk,1284
numpy/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/tests/test__all__.py,sha256=JziA96KUyXwWCPExbQcJBqe_RU1xQVrVwi1xhO8tzqM,230
numpy/tests/test_ctypeslib.py,sha256=a-lBRmW_gjLza_u0R1xhrzC3n_3R4MxCeLPBx8OS0Lo,12658
numpy/tests/test_matlib.py,sha256=TUaQmGoz9fvQQ8FrooTq-g9BFiViGWjoTIGQSUUF6-Y,1910
numpy/tests/test_numpy_version.py,sha256=N6ithCsZfV6UW5lVVMIPZQ-Yht5WIFplUOTSLmwtUNw,1619
numpy/tests/test_public_api.py,sha256=datvunWX9aZHWEXvZ5qU207aUpY78qUjcGqcZPUQIQ4,16417
numpy/tests/test_reloading.py,sha256=S4wjdcdS8CAR0BAUua71nIGR9tPKuyv4YFXxhCwQ4OQ,2308
numpy/tests/test_scripts.py,sha256=2srDs1QsLIkFiWmb4BVUCf3t_Pc3RBCRzRqFXjtdx-U,1619
numpy/tests/test_warnings.py,sha256=IMFVROBQqYZPibnHmwepGqEUQoBlDtdC8DlRulbMAd8,2354
numpy/typing/__init__.py,sha256=hqtUnjuh82MQdFCkR-XGlUzuUwtmLXu9-BSTkS13eCY,5406
numpy/typing/mypy_plugin.py,sha256=VFr2TjB2BN2HQNAXEzxJSGgE1YLWGXR7okzjX4vKeFM,6676
numpy/typing/setup.py,sha256=NpNqwxDwSxWBInw-7TJHIqEf3scDAczZwmzGxI_Ftn0,385
numpy/typing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/typing/tests/data/fail/arithmetic.pyi,sha256=W3Uqw-5lzAqlYUh3h9SBZqr4HC4xmRxMuNaQyRMVUPw,3971
numpy/typing/tests/data/fail/array_constructors.pyi,sha256=xmYGgCnxTHZ0nv5C8y-uuPsOnsQKXQjahE7muldp5cM,1122
numpy/typing/tests/data/fail/array_like.pyi,sha256=MUIx6Oc5bJeebr-TC4FhZFXnX9pJ5gQDv8moHmPek10,471
numpy/typing/tests/data/fail/array_pad.pyi,sha256=JGCMd_sRBYlsPQ2d7EfLaNooTsg1P0jBuD5Ds2MeXAg,138
numpy/typing/tests/data/fail/arrayprint.pyi,sha256=tNk1nyxQq45G-87GS_QiTlNJSxP7kdItvBqzVD2bCKY,563
numpy/typing/tests/data/fail/arrayterator.pyi,sha256=u1pNRrRUM8Q0o131lqGjvTQRy8-eHeazSvRArhnAyOo,494
numpy/typing/tests/data/fail/bitwise_ops.pyi,sha256=VBOUHgafxOWJQ_tXLb0Phpu93FbLZIgo7mF1-EVUZ0c,535
numpy/typing/tests/data/fail/char.pyi,sha256=xCEKTpdp5Al-Qn-hxkp6i-Cgw7HEk53pt2XAzvpd58Y,2681
numpy/typing/tests/data/fail/chararray.pyi,sha256=_a695QkkSHZ9utlqUYwVUumC2QGhGxcZteB0iGlrFug,2358
numpy/typing/tests/data/fail/comparisons.pyi,sha256=VjriRnjoRGFUsyCheiGo1s5qErQ5ajQW7fCBltxp3Zc,915
numpy/typing/tests/data/fail/constants.pyi,sha256=esbeTGlTUSu3v3jMJ6GBZ_j7Q7RsyJRB47MaKNZaCOk,293
numpy/typing/tests/data/fail/datasource.pyi,sha256=B05CkL35mBUyKmuvi5MF3vTkZRwc0SOJo_r0cKVBBuA,410
numpy/typing/tests/data/fail/dtype.pyi,sha256=ltT4BFaX_KTVdRLw2dMg3_OiSNYjDSNrXsxby6eeLTw,354
numpy/typing/tests/data/fail/einsumfunc.pyi,sha256=3NXuWXIpQBjSTBthdEz70XWpqIUhmp13Pjgmxnxl_w8,752
numpy/typing/tests/data/fail/false_positives.pyi,sha256=TKmRWDjlfVP2rgZczUMXcm9l0maPLDf7bSBon4Xfakw,377
numpy/typing/tests/data/fail/flatiter.pyi,sha256=zVjvKxKlUr1T4PUWCZg1PsQEBmfLFtX5eH3Q50390oI,868
numpy/typing/tests/data/fail/fromnumeric.pyi,sha256=XoM1AY9Ys2_XyN-xEF9css9vV3FZsF78jsSecmIEwtQ,5752
numpy/typing/tests/data/fail/histograms.pyi,sha256=OuKIQ42wtONC6mkJ5ZWOQ_jGOxj7xHUtFN3FeFdrHTU,437
numpy/typing/tests/data/fail/index_tricks.pyi,sha256=63ADYRCVtf0Dapc2dJpYJZDSIXK3MhhW_1lG30d3-RY,523
numpy/typing/tests/data/fail/lib_function_base.pyi,sha256=A_fZIktpi_2Q9zanq-Hq0zdKvMXyCfE6PpgUQNYILHY,2134
numpy/typing/tests/data/fail/lib_polynomial.pyi,sha256=CKOpJiyJzPfhyt01CwcqqCAbIJYD9TkYr7OEjRYU8is,942
numpy/typing/tests/data/fail/lib_utils.pyi,sha256=UGzb3HzY1lUdiwLcT_myFfL-nWedrkhxDt9mt68eokQ,289
numpy/typing/tests/data/fail/lib_version.pyi,sha256=JWtuTLcjkZpGfXshlFpJO5vINxawn9S-mxLGH0-7kcw,164
numpy/typing/tests/data/fail/linalg.pyi,sha256=j6GGpOENz0nuZsza0Dyfy6MtjfRltqrbY8K_7g5H92I,1370
numpy/typing/tests/data/fail/memmap.pyi,sha256=eAX-nEKtOb06mL8EPECukmL8MwrehSVRu5TBlHiSBaQ,164
numpy/typing/tests/data/fail/modules.pyi,sha256=0vg0lxV-ax5bRjYPZh2h6--JjvCYQkZkGbrkckZ2DmM,670
numpy/typing/tests/data/fail/multiarray.pyi,sha256=I4uoKVR-gsRtM577IrbEZbjyL0iGpRFPIKk3udj1xYM,1748
numpy/typing/tests/data/fail/ndarray.pyi,sha256=2I4smD6MlUD23Xx8Rt1gCtjj_m-tI5JEma-Z0unrgas,416
numpy/typing/tests/data/fail/ndarray_misc.pyi,sha256=ez2Wux0McE_cYgG4-6P5JgQCBV6-GthuhC9ywYdYAPY,1415
numpy/typing/tests/data/fail/nditer.pyi,sha256=We6p5_nmfUdd_4CtwYZc5O7MTSMyM-Xw7mEUzdKPcP4,333
numpy/typing/tests/data/fail/nested_sequence.pyi,sha256=7E1zJ2SZIF0ldbEmjtA_Bp6cV4Q-cS4Op0BJN3Vi3rc,444
numpy/typing/tests/data/fail/npyio.pyi,sha256=V_rwbNzIzRv7a3ySSoaCcM1i2WWyoESwWzwr1w9C1Vw,810
numpy/typing/tests/data/fail/numerictypes.pyi,sha256=gMj9jxXeI8nssH4B-8syNYXseb2NIkIXFHsWU7rRJJA,354
numpy/typing/tests/data/fail/random.pyi,sha256=d2JzeDW9mG4CKlF4S-foBmtB00y42toTDFcCw09bdKg,2891
numpy/typing/tests/data/fail/rec.pyi,sha256=BxH41lR1wLvLrlash9mzkPFngDAXSPQQXvuHxYylHAI,721
numpy/typing/tests/data/fail/scalars.pyi,sha256=d1giLvADI9LKnu--jXPYJQTYQ7ozU5NStDxqxhf094Q,3048
numpy/typing/tests/data/fail/shape_base.pyi,sha256=ZU1KSP0k-i-npwIMUhp42-EMzrdZhOqPEnV8ah-ZJ6U,160
numpy/typing/tests/data/fail/stride_tricks.pyi,sha256=L0fJGun6CDq24yNdw2zeNVGGcIpEOyP2dmWj1pEbMz8,324
numpy/typing/tests/data/fail/testing.pyi,sha256=O1nk5xnSvKn7aAHNi3mMLYIr75ym5WIT-BvZemEnayQ,1398
numpy/typing/tests/data/fail/twodim_base.pyi,sha256=jPdRkTn8fm_YTnZwOUZ-yNYE2fJT5X5rp56rlhjjovw,936
numpy/typing/tests/data/fail/type_check.pyi,sha256=0KG0c2LNUbUFChTYtbJ38eJUmfvUJl4Cn5G0vh1Bkrw,392
numpy/typing/tests/data/fail/ufunc_config.pyi,sha256=V5R7wY_P7KWn4IRxbLx42b1YF3wbIYzHEMr9BC41JHE,754
numpy/typing/tests/data/fail/ufunclike.pyi,sha256=wU7hNwN9SMRC8pMxOyDteWoCC4pDOdwfvk_ZCyhAY-c,700
numpy/typing/tests/data/fail/ufuncs.pyi,sha256=TiIj3qjjbAgNR0IahyYUGXDTA8AlSJLIKhDrfyzAHFw,1388
numpy/typing/tests/data/fail/warnings_and_errors.pyi,sha256=I96g0czQxXry3qxXChQ3BMwJJc79W6HXgBvYiyRmSXI,179
numpy/typing/tests/data/misc/extended_precision.pyi,sha256=-iSPpOqauIOyCLKYKr76J9dyKMTucyh61ZoABXEBWlk,364
numpy/typing/tests/data/mypy.ini,sha256=zLGw-qR5E8qtC06v7Oxlg-sRtRgFSWWgJ1CD7oopvwY,176
numpy/typing/tests/data/pass/arithmetic.py,sha256=hOdSrO-AVCt0F629T4KCISbhI05nccza8rscUICy-6I,7990
numpy/typing/tests/data/pass/array_constructors.py,sha256=lIy-MTR2YZZpVBTKndCEUHj-BtWGf21fN2yc2MVwSvE,2556
numpy/typing/tests/data/pass/array_like.py,sha256=mVIaUxN1HXw8Me_bK-TFFfRQRTPGj-TGakMG6NbCIso,957
numpy/typing/tests/data/pass/arrayprint.py,sha256=NTw1gJ9v3TDVwRov4zsg_27rI-ndKuG4mDidBWEKVyc,803
numpy/typing/tests/data/pass/arrayterator.py,sha256=z4o0H08T7tbzzMWhu5ZXdVqbivjBicuFgRHBk_lpOck,420
numpy/typing/tests/data/pass/bitwise_ops.py,sha256=zYz-ZNXpxjXDdo4fYWv_RQZq5af581dnaZrlu0-sSC8,1101
numpy/typing/tests/data/pass/comparisons.py,sha256=phjskmrmZ0bHTGLyNvo3mDuHCGR6I1OOj6S8ks71wRQ,3293
numpy/typing/tests/data/pass/dtype.py,sha256=7LeJ9EI_R2p15v-HmSRImS-wmWC1jwlgZykBuxBZCHg,1130
numpy/typing/tests/data/pass/einsumfunc.py,sha256=CXdLvQsU2iDqQc7d2TRRCSwguQzJ0SJDFn23SDeOOuY,1406
numpy/typing/tests/data/pass/flatiter.py,sha256=2xtMPvDgfhgjZIqiN3B3Wvy6Q9oBeo9uh4UkCAQNmwg,190
numpy/typing/tests/data/pass/fromnumeric.py,sha256=eM6RUBjVInsShXlblqq07-I0QwoST8n6g8WWuPSgYtA,4002
numpy/typing/tests/data/pass/index_tricks.py,sha256=Vn5iEuWlNdbr03zMEwAHvjBgI25-uCqRAJfUvRVWSp0,1556
numpy/typing/tests/data/pass/lib_utils.py,sha256=wHgHeuhfdv2cYi5_7xUv61wUnPEEmjc6xyFF6qj8wRY,445
numpy/typing/tests/data/pass/lib_version.py,sha256=TlLZK8sekCMm__WWo22FZfZc40zpczENf6y_TNjBpCw,317
numpy/typing/tests/data/pass/literal.py,sha256=7X2GZIjTiE-WUrBCKNi1FesDI_ENCc9RY_Lf3X2UVUE,1378
numpy/typing/tests/data/pass/mod.py,sha256=Intb9Ni_LlHhEka8kk81-601JCOl85jz_4_BQDA6iYI,1727
numpy/typing/tests/data/pass/modules.py,sha256=r6OC-qSjkLsR3uMpZVBahowrJp42i2t3LS7gCnuRIYo,638
numpy/typing/tests/data/pass/multiarray.py,sha256=i6VU-VN96Q16mRGzVoY3oTE2W1z16GOGTOVFxWGRacM,1407
numpy/typing/tests/data/pass/ndarray_conversion.py,sha256=-1iJDSvdD86k3yJCrWf1nouQrRHStf4cheiZ5OHFE78,1720
numpy/typing/tests/data/pass/ndarray_misc.py,sha256=0DqSoVWggfBJwP36X8zigaW4qPtu2ixvyTkXsUiQlV8,2901
numpy/typing/tests/data/pass/ndarray_shape_manipulation.py,sha256=yaBK3hW5fe2VpvARkn_NMeF-JX-OajI8JiRWOA_Uk7Y,687
numpy/typing/tests/data/pass/numeric.py,sha256=8AiyBs48ADbckFLK2JYyuEpnGi7G59cpQh-BBtaCUxc,1580
numpy/typing/tests/data/pass/numerictypes.py,sha256=s9OFag9m9t407msIIXpKln04zcD-6yAva9XKG1AqqQs,1020
numpy/typing/tests/data/pass/random.py,sha256=1TCtXCbuVb4IXijp4iNCWs_A4i892q_dDgt_yNKF46o,63306
numpy/typing/tests/data/pass/scalars.py,sha256=suluS3NqYd3Eg7R9WY18VCZiVN5b_-MRpZXw6gsOACI,3717
numpy/typing/tests/data/pass/simple.py,sha256=vVNV6ewDjVGAgsCbYxPE-j1EKg-u3_vDeEBDtgwCMq0,2849
numpy/typing/tests/data/pass/simple_py3.py,sha256=OBpoDmf5u4bRblugokiOZzufESsEmoU03MqipERrjLg,102
numpy/typing/tests/data/pass/ufunc_config.py,sha256=l1JiZe3VXYLzgvbuk42Mk6fd_nvLsc9aJ233W0_MLm0,1170
numpy/typing/tests/data/pass/ufunclike.py,sha256=vhQJlqPAZegEyC882iIU0uCqoA7ijTOZP1Vij-MrgEI,1085
numpy/typing/tests/data/pass/ufuncs.py,sha256=xGuFo9gOvT04Pj7iNOhRIAIj_NHSTykIFyGV2Oj_0yA,479
numpy/typing/tests/data/pass/warnings_and_errors.py,sha256=84SQc0-Le9MIy9rC9pQ4JchXsaaRNY1sqKwxxEaMNuE,156
numpy/typing/tests/data/reveal/arithmetic.pyi,sha256=apG__WZKDe4Mc4lBTd3B9bR7X0kzrv7JP_DVI9sCC7c,21331
numpy/typing/tests/data/reveal/array_constructors.pyi,sha256=RD2jAtvDwiKAWcTRl_ISH6hT_A9gfundqCpA_bmP__0,11776
numpy/typing/tests/data/reveal/arraypad.pyi,sha256=2Q2DxXCxLTbtu4jc4imoBNTho-KGCtdaBHT9neJs0rE,716
numpy/typing/tests/data/reveal/arrayprint.pyi,sha256=qa7LG2RcAbA5JC44_Z_t3CoIVGXy3qECmtwmRTmH-Ik,706
numpy/typing/tests/data/reveal/arraysetops.pyi,sha256=CfzXbyGl-NzFF27dTED8PGBgVdFbcXkatslHTrqbrtM,4731
numpy/typing/tests/data/reveal/arrayterator.pyi,sha256=mW99oYiREOyE_5VTFn1YTRIBMBaC8bjj80e_SHUkf0k,1152
numpy/typing/tests/data/reveal/bitwise_ops.pyi,sha256=e2OsUfoTxTdPdVZKJGeIP29jIdN01Nij1ESBsSsvQJU,3738
numpy/typing/tests/data/reveal/char.pyi,sha256=8xKiZ8vtqiO6RSUU2EDBGKlJcbvA3kfpEqsGTfS935s,8194
numpy/typing/tests/data/reveal/chararray.pyi,sha256=5VSe6nPmgwgYGYWPp3CchJyf52PcVVxlb5NP0qHWx_M,6444
numpy/typing/tests/data/reveal/comparisons.pyi,sha256=07azyt3l6QsqHVKmDy-dsfw9k5jhCVR2GWb13YXl1xY,7998
numpy/typing/tests/data/reveal/constants.pyi,sha256=F1YXi9jshwurwm6FcdTc1ofgHR-miMirBAwJb_rcxbw,1992
numpy/typing/tests/data/reveal/ctypeslib.pyi,sha256=ERqyLcyLecN56PyLJ1nvbIhweW0z-o91BY88ETjEJ-Y,5193
numpy/typing/tests/data/reveal/datasource.pyi,sha256=K4XZlEsYAbbzpD3KkkECzLH1fjategKa4PCwF1N8o3g,578
numpy/typing/tests/data/reveal/dtype.pyi,sha256=NbPckaTlu0Vxs4Fj45GWS3bCGGX9M-h6nLcqLva_lvg,2779
numpy/typing/tests/data/reveal/einsumfunc.pyi,sha256=ZRqzPazM5QV4saf48-dIT5CS4lXm5DQm0p_JZOw98Es,1919
numpy/typing/tests/data/reveal/emath.pyi,sha256=EjCPw2_u9PHiNGTbd0VUleysUqgbDGeo_rapTtNUU7Y,2590
numpy/typing/tests/data/reveal/false_positives.pyi,sha256=TexdGVvcIT9_ol2ldT0xp4ASh7JYXd-FdGvV6yAVX4c,359
numpy/typing/tests/data/reveal/fft.pyi,sha256=A41nHw8LDzRXbhFLOQoQRN69S_cizvIGZA0t507votQ,1887
numpy/typing/tests/data/reveal/flatiter.pyi,sha256=aI3RQ9Ylp5afg4o_qtl1rQFXGa24nkyv_RlR9l6XXKQ,874
numpy/typing/tests/data/reveal/fromnumeric.pyi,sha256=omVO5Fw9iSoP9m8owIRLgJOkiz-Hf_y6lWFP1Y2MK5k,13928
numpy/typing/tests/data/reveal/getlimits.pyi,sha256=dL9PAvLQbeWovNaqcppC1qaTtkoikjHCwILOmwGnys4,1594
numpy/typing/tests/data/reveal/histograms.pyi,sha256=zM4XlZjWTgqEJRg6ytcSdG7_i-2DEasyMPw_CAyvznE,1410
numpy/typing/tests/data/reveal/index_tricks.pyi,sha256=MlyUWGIsDdUMbbjBvmZ5urvnOJB06thxnHDP3yCfnLg,3547
numpy/typing/tests/data/reveal/lib_function_base.pyi,sha256=RIfOJ0IxIMw886AomAFKebFPISwXnUIQLMdluREa4bo,9388
numpy/typing/tests/data/reveal/lib_polynomial.pyi,sha256=CGaEX0DcuNd7whDr_n6xKUxgSwg3xP2SQ07ctMjVwaE,6464
numpy/typing/tests/data/reveal/lib_utils.pyi,sha256=hHc74Qzgc0sx-tSDd0UK4NeWsjzBoc19OGl0tBBq7Q8,947
numpy/typing/tests/data/reveal/lib_version.pyi,sha256=3uI1jeWbiyC2CEQ5-eqDkUZmb9871IywI6ZjetQUiUY,623
numpy/typing/tests/data/reveal/linalg.pyi,sha256=9FdN_kribCt6Pog1Zv4De1pTldTj8nlF83S4Yrk5LhQ,6486
numpy/typing/tests/data/reveal/matrix.pyi,sha256=88U9V-ZV3U603UcCSbm_pYj_AalrkE41Nz1Fqjdhs0M,3102
numpy/typing/tests/data/reveal/memmap.pyi,sha256=FaZG4pUGuwYprfYGjJh___DhP9iqkKWde4Ohu2oBdZA,773
numpy/typing/tests/data/reveal/mod.pyi,sha256=irO1YSd5eF5VbFnOrYe1webn6Z-QtWtwE387SB8WHes,6136
numpy/typing/tests/data/reveal/modules.pyi,sha256=wmqhjyy7bQ_wkmyuuktHG_mTKY-ARCpmz8WNHOnkhdQ,1957
numpy/typing/tests/data/reveal/multiarray.pyi,sha256=3iNrvHD2Fgv5sGlwLyGHn1Mmj0Cj4nRPenVgf7_VgK0,5814
numpy/typing/tests/data/reveal/nbit_base_example.pyi,sha256=XoIdrl6HtWMd9TbtGljdUeGWbTnVEviTh5ugCCl-Fmc,521
numpy/typing/tests/data/reveal/ndarray_conversion.pyi,sha256=DZgkD8FQ0SGNVCoBHvDbRad2Xe_8_hLMBcTYzSWME8k,1964
numpy/typing/tests/data/reveal/ndarray_misc.pyi,sha256=jn5nSu6uuORXJFjQMhYe97wgsQ1FBTL0kdjyxih3KZk,7973
numpy/typing/tests/data/reveal/ndarray_shape_manipulation.pyi,sha256=-ITCn7JR2GFXdv8n_bJwFZCsVUCxP7BNAjtLM7ry43Y,939
numpy/typing/tests/data/reveal/nditer.pyi,sha256=yM7JnrOFG-8eDNcrJX-RSS6hTANKZVLK7SYw5QKGZfE,2113
numpy/typing/tests/data/reveal/nested_sequence.pyi,sha256=7N3Bwm8ut6kFzXsSMZXFenKpIzMwgqc9pGjWTi06QCA,674
numpy/typing/tests/data/reveal/npyio.pyi,sha256=DPqyaSd3D1fuTjp7O_6pt1KsniRBIixc1pPW4KtG3po,4556
numpy/typing/tests/data/reveal/numeric.pyi,sha256=a3SKVGUfZocx65sTiaQuyQ7nOp23xXrqkhN4hZybR28,6935
numpy/typing/tests/data/reveal/numerictypes.pyi,sha256=XJ9rx-lkP546MK3SVH3fVPEphVbjq5T2g_25lik7T84,1828
numpy/typing/tests/data/reveal/random.pyi,sha256=E6YpwwL49FhowEuusOI4tRL3MJDGytRdkWhqFXE8kyw,130927
numpy/typing/tests/data/reveal/rec.pyi,sha256=Udb8qEAJbPeujAExTHUDX-QYK4JWU2PZfn1_AaDQm4M,3508
numpy/typing/tests/data/reveal/scalars.pyi,sha256=VIpkYUtSwqQNBV-faOUBPbyLQGocWL7DhG-LbrTaCAI,5860
numpy/typing/tests/data/reveal/shape_base.pyi,sha256=cASKYHfJXDLTzZP_BGVe2aaLaO2Id87apHp3hT9ZtGE,2689
numpy/typing/tests/data/reveal/stride_tricks.pyi,sha256=N84pbKt3mia0RvaUybnG0DajzD06KzGdx3KWgS52dMg,1591
numpy/typing/tests/data/reveal/testing.pyi,sha256=O7d3jBfsFo624u4XgyrFLPj2rLFwV_BkDcdDIGgAtdI,9198
numpy/typing/tests/data/reveal/twodim_base.pyi,sha256=IckHF7P1dkoasCOvT0Z-xkAi7eBojlnkKxjU73CzftE,3399
numpy/typing/tests/data/reveal/type_check.pyi,sha256=2E7-WbhYNkg9pbzIsOk7TZeNo9Vn5IgXERkF0n8Qt9g,3104
numpy/typing/tests/data/reveal/ufunc_config.pyi,sha256=BhpX7d0FgYNw4IxJLIwWiW2Cmxg-Y2-vmnmc3o-Hbsc,1329
numpy/typing/tests/data/reveal/ufunclike.pyi,sha256=Hs7ur6h5ULgo3uo8nyVTGbt-kGMGi_SSpkoEWZwK8kM,1348
numpy/typing/tests/data/reveal/ufuncs.pyi,sha256=4kVZPNsH_tFX8J_86jjswqYLugfmQFTOQCY4_Iaue0w,2987
numpy/typing/tests/data/reveal/version.pyi,sha256=sDVJ6IuDtzgObpzUOzOBUzDVAvY1zKgKFe1OYratqu0,321
numpy/typing/tests/data/reveal/warnings_and_errors.pyi,sha256=ihpczQWLcIA9i6pVFwCVGU3pgc6AltQfrl6Fx5XplBo,429
numpy/typing/tests/test_generic_alias.py,sha256=LIhDqr4ADxFNdirzre-CRksa8_yOSbawan5cWVrqn_k,7131
numpy/typing/tests/test_isfile.py,sha256=KEPwqJBkXEbMRPCtSxFYULfPNipG5dyY2BRYwPI4Lu8,842
numpy/typing/tests/test_runtime.py,sha256=rlZjCEOhr7eM_FYvLmZg8zJk_ydhkYJGOxK3cC0p9sk,2533
numpy/typing/tests/test_typing.py,sha256=TLqNy-StRZdGA3_EUW_CvzJAqQtMTpxpNJ4iWqtvvis,15767
numpy/version.py,sha256=Ph0IYh9ExfFLZ1azhCwkO5pQGfNk_5ICpLaQ4Mfl3vU,490
